{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\components\\\\Predictive\\\\PredictiveSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Settings, Brain, Clock, AlertTriangle, Save, RefreshCw, Play, Pause, Info, CheckCircle } from 'lucide-react';\nimport { predictiveAPI } from '../../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PredictiveSettings = () => {\n  _s();\n  var _syncStatus$total_ins, _syncStatus$mapped_pr, _syncStatus$total_vel;\n  const [settings, setSettings] = useState({\n    forecastHorizonDays: 30,\n    confidenceInterval: 0.95,\n    reorderThreshold: 0.8,\n    criticalStockDays: 3,\n    lowStockDays: 7,\n    safetyStockDays: 5,\n    leadTimeDays: 7,\n    enableAutomaticForecasting: true,\n    enableAutomaticReordering: false,\n    forecastUpdateFrequency: 'daily',\n    reorderSuggestionFrequency: 'hourly'\n  });\n  const [schedulerStatus, setSchedulerStatus] = useState(null);\n  const [syncStatus, setSyncStatus] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  useEffect(() => {\n    fetchSchedulerStatus();\n    fetchSyncStatus();\n  }, []);\n  const fetchSchedulerStatus = async () => {\n    try {\n      const response = await predictiveAPI.getSchedulerStatus();\n      setSchedulerStatus(response.data);\n    } catch (error) {\n      console.error('Error fetching scheduler status:', error);\n    }\n  };\n  const fetchSyncStatus = async () => {\n    try {\n      const response = await predictiveAPI.getSyncStatus();\n      setSyncStatus(response.data);\n    } catch (error) {\n      console.error('Error fetching sync status:', error);\n    }\n  };\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleSaveSettings = async () => {\n    try {\n      setSaving(true);\n      // In a real implementation, you would save these to a backend endpoint\n      // For now, we'll just save to localStorage\n      localStorage.setItem('predictiveSettings', JSON.stringify(settings));\n      toast.success('Settings saved successfully');\n    } catch (error) {\n      console.error('Error saving settings:', error);\n      toast.error('Failed to save settings');\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleTriggerJob = async jobName => {\n    try {\n      setLoading(true);\n      toast.loading(`Triggering ${jobName}...`, {\n        id: 'trigger-job'\n      });\n      await predictiveAPI.triggerSchedulerJob(jobName);\n      toast.success(`${jobName} triggered successfully`, {\n        id: 'trigger-job'\n      });\n      await fetchSchedulerStatus();\n    } catch (error) {\n      console.error(`Error triggering ${jobName}:`, error);\n      toast.error(`Failed to trigger ${jobName}`, {\n        id: 'trigger-job'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSync = async syncType => {\n    try {\n      setLoading(true);\n      toast.loading(`Starting ${syncType} sync...`, {\n        id: 'sync-operation'\n      });\n      let result;\n      switch (syncType) {\n        case 'products':\n          result = await predictiveAPI.syncProducts();\n          break;\n        case 'velocity':\n          result = await predictiveAPI.syncVelocity();\n          break;\n        case 'full':\n          result = await predictiveAPI.fullSync();\n          break;\n        default:\n          throw new Error('Unknown sync type');\n      }\n      toast.success(`${syncType} sync completed successfully`, {\n        id: 'sync-operation'\n      });\n      await fetchSyncStatus();\n    } catch (error) {\n      console.error(`Error during ${syncType} sync:`, error);\n      toast.error(`Failed to sync ${syncType}`, {\n        id: 'sync-operation'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const SettingCard = ({\n    title,\n    description,\n    children\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 mt-1\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n  const NumberInput = ({\n    label,\n    value,\n    onChange,\n    min,\n    max,\n    step = 1,\n    suffix = ''\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-between\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"text-sm font-medium text-gray-700\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"number\",\n        min: min,\n        max: max,\n        step: step,\n        value: value,\n        onChange: e => onChange(parseFloat(e.target.value)),\n        className: \"w-20 px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), suffix && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm text-gray-500\",\n        children: suffix\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 20\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n  const ToggleSwitch = ({\n    label,\n    description,\n    checked,\n    onChange\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-between\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-sm font-medium text-gray-700\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), description && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500 mt-1\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"button\",\n      onClick: () => onChange(!checked),\n      className: `relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${checked ? 'bg-blue-600' : 'bg-gray-200'}`,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${checked ? 'translate-x-5' : 'translate-x-0'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this);\n  const SelectInput = ({\n    label,\n    value,\n    onChange,\n    options\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-between\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"text-sm font-medium text-gray-700\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      value: value,\n      onChange: e => onChange(e.target.value),\n      className: \"px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n      children: options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: option.value,\n        children: option.label\n      }, option.value, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Settings, {\n            className: \"h-8 w-8 mr-3 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), \"Predictive Analytics Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-1\",\n          children: \"Configure forecasting parameters and automation settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSaveSettings,\n        disabled: saving,\n        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors\",\n        children: [saving ? /*#__PURE__*/_jsxDEV(RefreshCw, {\n          className: \"h-4 w-4 mr-2 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Save, {\n          className: \"h-4 w-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this), \"Save Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(SettingCard, {\n        title: \"Forecasting Parameters\",\n        description: \"Configure how demand forecasts are generated\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(NumberInput, {\n            label: \"Forecast Horizon\",\n            value: settings.forecastHorizonDays,\n            onChange: value => handleSettingChange('forecastHorizonDays', value),\n            min: 7,\n            max: 365,\n            suffix: \"days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NumberInput, {\n            label: \"Confidence Interval\",\n            value: settings.confidenceInterval,\n            onChange: value => handleSettingChange('confidenceInterval', value),\n            min: 0.8,\n            max: 0.99,\n            step: 0.01,\n            suffix: \"%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SelectInput, {\n            label: \"Update Frequency\",\n            value: settings.forecastUpdateFrequency,\n            onChange: value => handleSettingChange('forecastUpdateFrequency', value),\n            options: [{\n              value: 'hourly',\n              label: 'Hourly'\n            }, {\n              value: 'daily',\n              label: 'Daily'\n            }, {\n              value: 'weekly',\n              label: 'Weekly'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingCard, {\n        title: \"Reorder Settings\",\n        description: \"Configure automatic reorder suggestions and thresholds\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(NumberInput, {\n            label: \"Reorder Threshold\",\n            value: settings.reorderThreshold,\n            onChange: value => handleSettingChange('reorderThreshold', value),\n            min: 0.1,\n            max: 1.0,\n            step: 0.1,\n            suffix: \"ratio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NumberInput, {\n            label: \"Critical Stock Alert\",\n            value: settings.criticalStockDays,\n            onChange: value => handleSettingChange('criticalStockDays', value),\n            min: 1,\n            max: 14,\n            suffix: \"days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NumberInput, {\n            label: \"Low Stock Alert\",\n            value: settings.lowStockDays,\n            onChange: value => handleSettingChange('lowStockDays', value),\n            min: 3,\n            max: 30,\n            suffix: \"days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NumberInput, {\n            label: \"Safety Stock\",\n            value: settings.safetyStockDays,\n            onChange: value => handleSettingChange('safetyStockDays', value),\n            min: 1,\n            max: 30,\n            suffix: \"days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NumberInput, {\n            label: \"Default Lead Time\",\n            value: settings.leadTimeDays,\n            onChange: value => handleSettingChange('leadTimeDays', value),\n            min: 1,\n            max: 60,\n            suffix: \"days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingCard, {\n        title: \"Automation Settings\",\n        description: \"Enable or disable automatic processes\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(ToggleSwitch, {\n            label: \"Automatic Forecasting\",\n            description: \"Automatically update forecasts based on schedule\",\n            checked: settings.enableAutomaticForecasting,\n            onChange: value => handleSettingChange('enableAutomaticForecasting', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n            label: \"Automatic Reordering\",\n            description: \"Automatically generate reorder suggestions\",\n            checked: settings.enableAutomaticReordering,\n            onChange: value => handleSettingChange('enableAutomaticReordering', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SelectInput, {\n            label: \"Suggestion Frequency\",\n            value: settings.reorderSuggestionFrequency,\n            onChange: value => handleSettingChange('reorderSuggestionFrequency', value),\n            options: [{\n              value: 'hourly',\n              label: 'Hourly'\n            }, {\n              value: 'daily',\n              label: 'Daily'\n            }, {\n              value: 'weekly',\n              label: 'Weekly'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingCard, {\n        title: \"Scheduler Status\",\n        description: \"Monitor and control automated processes\",\n        children: schedulerStatus ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: Object.entries(schedulerStatus.scheduler_status).map(([jobName, status]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900 capitalize\",\n                  children: jobName.replace(/([A-Z])/g, ' $1').trim()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [status.running ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"h-4 w-4 text-green-500 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(Clock, {\n                    className: \"h-4 w-4 text-gray-400 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: status.running ? 'Running' : 'Stopped'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleTriggerJob(jobName),\n                disabled: loading,\n                className: \"text-blue-600 hover:text-blue-700 disabled:opacity-50\",\n                title: \"Trigger manually\",\n                children: /*#__PURE__*/_jsxDEV(Play, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 21\n              }, this)]\n            }, jobName, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 text-center\",\n            children: [\"Last updated: \", new Date(schedulerStatus.server_time).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            className: \"h-8 w-8 mx-auto mb-2 text-gray-300 animate-spin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"Loading scheduler status...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingCard, {\n      title: \"Instacart Data Synchronization\",\n      description: \"Sync product catalog and sales data from Instacart market basket analysis\",\n      children: syncStatus ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 rounded-lg p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs font-medium text-blue-600 uppercase tracking-wide\",\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-blue-900\",\n              children: (_syncStatus$total_ins = syncStatus.total_instacart_products) === null || _syncStatus$total_ins === void 0 ? void 0 : _syncStatus$total_ins.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-50 rounded-lg p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs font-medium text-green-600 uppercase tracking-wide\",\n              children: \"Mapped Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-green-900\",\n              children: (_syncStatus$mapped_pr = syncStatus.mapped_products) === null || _syncStatus$mapped_pr === void 0 ? void 0 : _syncStatus$mapped_pr.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-purple-50 rounded-lg p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs font-medium text-purple-600 uppercase tracking-wide\",\n              children: \"Mapping %\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-purple-900\",\n              children: [syncStatus.mapping_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-orange-50 rounded-lg p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs font-medium text-orange-600 uppercase tracking-wide\",\n              children: \"Velocity Records\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-orange-900\",\n              children: (_syncStatus$total_vel = syncStatus.total_velocity_records) === null || _syncStatus$total_vel === void 0 ? void 0 : _syncStatus$total_vel.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSync('products'),\n            disabled: loading,\n            className: \"inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 disabled:opacity-50 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: `h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this), \"Sync Products\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSync('velocity'),\n            disabled: loading,\n            className: \"inline-flex items-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 disabled:opacity-50 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: `h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 17\n            }, this), \"Sync Velocity\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSync('full'),\n            disabled: loading,\n            className: \"inline-flex items-center px-3 py-2 border border-purple-300 text-sm font-medium rounded-md text-purple-700 bg-purple-50 hover:bg-purple-100 disabled:opacity-50 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: `h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), \"Full Sync\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 13\n        }, this), syncStatus.last_sync_date && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-500 text-center\",\n          children: [\"Last sync: \", new Date(syncStatus.last_sync_date).toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          className: \"h-8 w-8 mx-auto mb-2 text-gray-300 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Loading sync status...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(Info, {\n          className: \"h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-blue-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium mb-2\",\n            children: \"Configuration Tips:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Forecast Horizon: Longer horizons provide more strategic planning but may be less accurate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Confidence Interval: Higher values provide wider prediction bands but more conservative estimates\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Safety Stock: Buffer inventory to handle demand variability and supply delays\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Lead Time: Time between placing an order and receiving inventory\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 5\n  }, this);\n};\n_s(PredictiveSettings, \"cyEAYOSxj85h/ZnpSqvwET2Z/tw=\");\n_c = PredictiveSettings;\nexport default PredictiveSettings;\nvar _c;\n$RefreshReg$(_c, \"PredictiveSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Settings", "Brain", "Clock", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Save", "RefreshCw", "Play", "Pause", "Info", "CheckCircle", "predictiveAPI", "toast", "jsxDEV", "_jsxDEV", "PredictiveSettings", "_s", "_syncStatus$total_ins", "_syncStatus$mapped_pr", "_syncStatus$total_vel", "settings", "setSettings", "forecastHorizonDays", "confidenceInterval", "reorderThreshold", "criticalStockDays", "lowStockDays", "safetyStockDays", "leadTimeDays", "enableAutomaticForecasting", "enableAutomaticReordering", "forecastUpdateFrequency", "reorderSuggestionFrequency", "schedulerStatus", "setSchedulerStatus", "syncStatus", "setSyncStatus", "loading", "setLoading", "saving", "setSaving", "fetchSchedulerStatus", "fetchSyncStatus", "response", "getSchedulerStatus", "data", "error", "console", "getSyncStatus", "handleSettingChange", "key", "value", "prev", "handleSaveSettings", "localStorage", "setItem", "JSON", "stringify", "success", "handleTriggerJob", "job<PERSON>ame", "id", "triggerSchedulerJob", "handleSync", "syncType", "result", "syncProducts", "syncVelocity", "fullSync", "Error", "SettingCard", "title", "description", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "NumberInput", "label", "onChange", "min", "max", "step", "suffix", "type", "e", "parseFloat", "target", "ToggleSwitch", "checked", "onClick", "SelectInput", "options", "map", "option", "disabled", "Object", "entries", "scheduler_status", "status", "replace", "trim", "running", "Date", "server_time", "toLocaleString", "total_instacart_products", "mapped_products", "mapping_percentage", "total_velocity_records", "last_sync_date", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/components/Predictive/PredictiveSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Setting<PERSON>,\n  <PERSON>,\n  Clock,\n  AlertTriangle,\n  Save,\n  RefreshCw,\n  Play,\n  Pause,\n  Info,\n  CheckCircle\n} from 'lucide-react';\nimport { predictiveAPI } from '../../services/api';\nimport toast from 'react-hot-toast';\n\nconst PredictiveSettings = () => {\n  const [settings, setSettings] = useState({\n    forecastHorizonDays: 30,\n    confidenceInterval: 0.95,\n    reorderThreshold: 0.8,\n    criticalStockDays: 3,\n    lowStockDays: 7,\n    safetyStockDays: 5,\n    leadTimeDays: 7,\n    enableAutomaticForecasting: true,\n    enableAutomaticReordering: false,\n    forecastUpdateFrequency: 'daily',\n    reorderSuggestionFrequency: 'hourly'\n  });\n\n  const [schedulerStatus, setSchedulerStatus] = useState(null);\n  const [syncStatus, setSyncStatus] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  useEffect(() => {\n    fetchSchedulerStatus();\n    fetchSyncStatus();\n  }, []);\n\n  const fetchSchedulerStatus = async () => {\n    try {\n      const response = await predictiveAPI.getSchedulerStatus();\n      setSchedulerStatus(response.data);\n    } catch (error) {\n      console.error('Error fetching scheduler status:', error);\n    }\n  };\n\n  const fetchSyncStatus = async () => {\n    try {\n      const response = await predictiveAPI.getSyncStatus();\n      setSyncStatus(response.data);\n    } catch (error) {\n      console.error('Error fetching sync status:', error);\n    }\n  };\n\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handleSaveSettings = async () => {\n    try {\n      setSaving(true);\n      // In a real implementation, you would save these to a backend endpoint\n      // For now, we'll just save to localStorage\n      localStorage.setItem('predictiveSettings', JSON.stringify(settings));\n      toast.success('Settings saved successfully');\n    } catch (error) {\n      console.error('Error saving settings:', error);\n      toast.error('Failed to save settings');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleTriggerJob = async (jobName) => {\n    try {\n      setLoading(true);\n      toast.loading(`Triggering ${jobName}...`, { id: 'trigger-job' });\n\n      await predictiveAPI.triggerSchedulerJob(jobName);\n\n      toast.success(`${jobName} triggered successfully`, { id: 'trigger-job' });\n      await fetchSchedulerStatus();\n    } catch (error) {\n      console.error(`Error triggering ${jobName}:`, error);\n      toast.error(`Failed to trigger ${jobName}`, { id: 'trigger-job' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSync = async (syncType) => {\n    try {\n      setLoading(true);\n      toast.loading(`Starting ${syncType} sync...`, { id: 'sync-operation' });\n\n      let result;\n      switch (syncType) {\n        case 'products':\n          result = await predictiveAPI.syncProducts();\n          break;\n        case 'velocity':\n          result = await predictiveAPI.syncVelocity();\n          break;\n        case 'full':\n          result = await predictiveAPI.fullSync();\n          break;\n        default:\n          throw new Error('Unknown sync type');\n      }\n\n      toast.success(`${syncType} sync completed successfully`, { id: 'sync-operation' });\n      await fetchSyncStatus();\n    } catch (error) {\n      console.error(`Error during ${syncType} sync:`, error);\n      toast.error(`Failed to sync ${syncType}`, { id: 'sync-operation' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const SettingCard = ({ title, description, children }) => (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"mb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\n        <p className=\"text-sm text-gray-600 mt-1\">{description}</p>\n      </div>\n      {children}\n    </div>\n  );\n\n  const NumberInput = ({ label, value, onChange, min, max, step = 1, suffix = '' }) => (\n    <div className=\"flex items-center justify-between\">\n      <label className=\"text-sm font-medium text-gray-700\">{label}</label>\n      <div className=\"flex items-center space-x-2\">\n        <input\n          type=\"number\"\n          min={min}\n          max={max}\n          step={step}\n          value={value}\n          onChange={(e) => onChange(parseFloat(e.target.value))}\n          className=\"w-20 px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        />\n        {suffix && <span className=\"text-sm text-gray-500\">{suffix}</span>}\n      </div>\n    </div>\n  );\n\n  const ToggleSwitch = ({ label, description, checked, onChange }) => (\n    <div className=\"flex items-center justify-between\">\n      <div className=\"flex-1\">\n        <label className=\"text-sm font-medium text-gray-700\">{label}</label>\n        {description && <p className=\"text-xs text-gray-500 mt-1\">{description}</p>}\n      </div>\n      <button\n        type=\"button\"\n        onClick={() => onChange(!checked)}\n        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${\n          checked ? 'bg-blue-600' : 'bg-gray-200'\n        }`}\n      >\n        <span\n          className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${\n            checked ? 'translate-x-5' : 'translate-x-0'\n          }`}\n        />\n      </button>\n    </div>\n  );\n\n  const SelectInput = ({ label, value, onChange, options }) => (\n    <div className=\"flex items-center justify-between\">\n      <label className=\"text-sm font-medium text-gray-700\">{label}</label>\n      <select\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        className=\"px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n      >\n        {options.map(option => (\n          <option key={option.value} value={option.value}>\n            {option.label}\n          </option>\n        ))}\n      </select>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n            <Settings className=\"h-8 w-8 mr-3 text-blue-600\" />\n            Predictive Analytics Settings\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            Configure forecasting parameters and automation settings\n          </p>\n        </div>\n        \n        <button\n          onClick={handleSaveSettings}\n          disabled={saving}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n        >\n          {saving ? (\n            <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n          ) : (\n            <Save className=\"h-4 w-4 mr-2\" />\n          )}\n          Save Settings\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Forecasting Parameters */}\n        <SettingCard\n          title=\"Forecasting Parameters\"\n          description=\"Configure how demand forecasts are generated\"\n        >\n          <div className=\"space-y-4\">\n            <NumberInput\n              label=\"Forecast Horizon\"\n              value={settings.forecastHorizonDays}\n              onChange={(value) => handleSettingChange('forecastHorizonDays', value)}\n              min={7}\n              max={365}\n              suffix=\"days\"\n            />\n            \n            <NumberInput\n              label=\"Confidence Interval\"\n              value={settings.confidenceInterval}\n              onChange={(value) => handleSettingChange('confidenceInterval', value)}\n              min={0.8}\n              max={0.99}\n              step={0.01}\n              suffix=\"%\"\n            />\n            \n            <SelectInput\n              label=\"Update Frequency\"\n              value={settings.forecastUpdateFrequency}\n              onChange={(value) => handleSettingChange('forecastUpdateFrequency', value)}\n              options={[\n                { value: 'hourly', label: 'Hourly' },\n                { value: 'daily', label: 'Daily' },\n                { value: 'weekly', label: 'Weekly' }\n              ]}\n            />\n          </div>\n        </SettingCard>\n\n        {/* Reorder Settings */}\n        <SettingCard\n          title=\"Reorder Settings\"\n          description=\"Configure automatic reorder suggestions and thresholds\"\n        >\n          <div className=\"space-y-4\">\n            <NumberInput\n              label=\"Reorder Threshold\"\n              value={settings.reorderThreshold}\n              onChange={(value) => handleSettingChange('reorderThreshold', value)}\n              min={0.1}\n              max={1.0}\n              step={0.1}\n              suffix=\"ratio\"\n            />\n            \n            <NumberInput\n              label=\"Critical Stock Alert\"\n              value={settings.criticalStockDays}\n              onChange={(value) => handleSettingChange('criticalStockDays', value)}\n              min={1}\n              max={14}\n              suffix=\"days\"\n            />\n            \n            <NumberInput\n              label=\"Low Stock Alert\"\n              value={settings.lowStockDays}\n              onChange={(value) => handleSettingChange('lowStockDays', value)}\n              min={3}\n              max={30}\n              suffix=\"days\"\n            />\n            \n            <NumberInput\n              label=\"Safety Stock\"\n              value={settings.safetyStockDays}\n              onChange={(value) => handleSettingChange('safetyStockDays', value)}\n              min={1}\n              max={30}\n              suffix=\"days\"\n            />\n            \n            <NumberInput\n              label=\"Default Lead Time\"\n              value={settings.leadTimeDays}\n              onChange={(value) => handleSettingChange('leadTimeDays', value)}\n              min={1}\n              max={60}\n              suffix=\"days\"\n            />\n          </div>\n        </SettingCard>\n\n        {/* Automation Settings */}\n        <SettingCard\n          title=\"Automation Settings\"\n          description=\"Enable or disable automatic processes\"\n        >\n          <div className=\"space-y-4\">\n            <ToggleSwitch\n              label=\"Automatic Forecasting\"\n              description=\"Automatically update forecasts based on schedule\"\n              checked={settings.enableAutomaticForecasting}\n              onChange={(value) => handleSettingChange('enableAutomaticForecasting', value)}\n            />\n            \n            <ToggleSwitch\n              label=\"Automatic Reordering\"\n              description=\"Automatically generate reorder suggestions\"\n              checked={settings.enableAutomaticReordering}\n              onChange={(value) => handleSettingChange('enableAutomaticReordering', value)}\n            />\n            \n            <SelectInput\n              label=\"Suggestion Frequency\"\n              value={settings.reorderSuggestionFrequency}\n              onChange={(value) => handleSettingChange('reorderSuggestionFrequency', value)}\n              options={[\n                { value: 'hourly', label: 'Hourly' },\n                { value: 'daily', label: 'Daily' },\n                { value: 'weekly', label: 'Weekly' }\n              ]}\n            />\n          </div>\n        </SettingCard>\n\n        {/* Scheduler Status */}\n        <SettingCard\n          title=\"Scheduler Status\"\n          description=\"Monitor and control automated processes\"\n        >\n          {schedulerStatus ? (\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                {Object.entries(schedulerStatus.scheduler_status).map(([jobName, status]) => (\n                  <div key={jobName} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900 capitalize\">\n                        {jobName.replace(/([A-Z])/g, ' $1').trim()}\n                      </p>\n                      <div className=\"flex items-center mt-1\">\n                        {status.running ? (\n                          <CheckCircle className=\"h-4 w-4 text-green-500 mr-1\" />\n                        ) : (\n                          <Clock className=\"h-4 w-4 text-gray-400 mr-1\" />\n                        )}\n                        <span className=\"text-xs text-gray-500\">\n                          {status.running ? 'Running' : 'Stopped'}\n                        </span>\n                      </div>\n                    </div>\n                    <button\n                      onClick={() => handleTriggerJob(jobName)}\n                      disabled={loading}\n                      className=\"text-blue-600 hover:text-blue-700 disabled:opacity-50\"\n                      title=\"Trigger manually\"\n                    >\n                      <Play className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n              \n              <div className=\"text-xs text-gray-500 text-center\">\n                Last updated: {new Date(schedulerStatus.server_time).toLocaleString()}\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-4\">\n              <RefreshCw className=\"h-8 w-8 mx-auto mb-2 text-gray-300 animate-spin\" />\n              <p className=\"text-gray-500\">Loading scheduler status...</p>\n            </div>\n          )}\n        </SettingCard>\n      </div>\n\n      {/* Data Synchronization Status */}\n      <SettingCard\n        title=\"Instacart Data Synchronization\"\n        description=\"Sync product catalog and sales data from Instacart market basket analysis\"\n      >\n        {syncStatus ? (\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div className=\"bg-blue-50 rounded-lg p-3\">\n                <div className=\"text-xs font-medium text-blue-600 uppercase tracking-wide\">\n                  Total Products\n                </div>\n                <div className=\"text-lg font-bold text-blue-900\">\n                  {syncStatus.total_instacart_products?.toLocaleString()}\n                </div>\n              </div>\n              <div className=\"bg-green-50 rounded-lg p-3\">\n                <div className=\"text-xs font-medium text-green-600 uppercase tracking-wide\">\n                  Mapped Products\n                </div>\n                <div className=\"text-lg font-bold text-green-900\">\n                  {syncStatus.mapped_products?.toLocaleString()}\n                </div>\n              </div>\n              <div className=\"bg-purple-50 rounded-lg p-3\">\n                <div className=\"text-xs font-medium text-purple-600 uppercase tracking-wide\">\n                  Mapping %\n                </div>\n                <div className=\"text-lg font-bold text-purple-900\">\n                  {syncStatus.mapping_percentage}%\n                </div>\n              </div>\n              <div className=\"bg-orange-50 rounded-lg p-3\">\n                <div className=\"text-xs font-medium text-orange-600 uppercase tracking-wide\">\n                  Velocity Records\n                </div>\n                <div className=\"text-lg font-bold text-orange-900\">\n                  {syncStatus.total_velocity_records?.toLocaleString()}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => handleSync('products')}\n                disabled={loading}\n                className=\"inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 disabled:opacity-50 transition-colors\"\n              >\n                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\n                Sync Products\n              </button>\n              <button\n                onClick={() => handleSync('velocity')}\n                disabled={loading}\n                className=\"inline-flex items-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 disabled:opacity-50 transition-colors\"\n              >\n                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\n                Sync Velocity\n              </button>\n              <button\n                onClick={() => handleSync('full')}\n                disabled={loading}\n                className=\"inline-flex items-center px-3 py-2 border border-purple-300 text-sm font-medium rounded-md text-purple-700 bg-purple-50 hover:bg-purple-100 disabled:opacity-50 transition-colors\"\n              >\n                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\n                Full Sync\n              </button>\n            </div>\n\n            {syncStatus.last_sync_date && (\n              <div className=\"text-xs text-gray-500 text-center\">\n                Last sync: {new Date(syncStatus.last_sync_date).toLocaleString()}\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"text-center py-4\">\n            <RefreshCw className=\"h-8 w-8 mx-auto mb-2 text-gray-300 animate-spin\" />\n            <p className=\"text-gray-500\">Loading sync status...</p>\n          </div>\n        )}\n      </SettingCard>\n\n      {/* Information Panel */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-start\">\n          <Info className=\"h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0\" />\n          <div className=\"text-sm text-blue-800\">\n            <p className=\"font-medium mb-2\">Configuration Tips:</p>\n            <ul className=\"space-y-1 text-xs\">\n              <li>• Forecast Horizon: Longer horizons provide more strategic planning but may be less accurate</li>\n              <li>• Confidence Interval: Higher values provide wider prediction bands but more conservative estimates</li>\n              <li>• Safety Stock: Buffer inventory to handle demand variability and supply delays</li>\n              <li>• Lead Time: Time between placing an order and receiving inventory</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PredictiveSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,WAAW,QACN,cAAc;AACrB,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,mBAAmB,EAAE,EAAE;IACvBC,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,GAAG;IACrBC,iBAAiB,EAAE,CAAC;IACpBC,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,0BAA0B,EAAE,IAAI;IAChCC,yBAAyB,EAAE,KAAK;IAChCC,uBAAuB,EAAE,OAAO;IAChCC,0BAA0B,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACdyC,oBAAoB,CAAC,CAAC;IACtBC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMhC,aAAa,CAACiC,kBAAkB,CAAC,CAAC;MACzDV,kBAAkB,CAACS,QAAQ,CAACE,IAAI,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;EAED,MAAMJ,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhC,aAAa,CAACqC,aAAa,CAAC,CAAC;MACpDZ,aAAa,CAACO,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC1C9B,WAAW,CAAC+B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFb,SAAS,CAAC,IAAI,CAAC;MACf;MACA;MACAc,YAAY,CAACC,OAAO,CAAC,oBAAoB,EAAEC,IAAI,CAACC,SAAS,CAACrC,QAAQ,CAAC,CAAC;MACpER,KAAK,CAAC8C,OAAO,CAAC,6BAA6B,CAAC;IAC9C,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9ClC,KAAK,CAACkC,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACRN,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChB1B,KAAK,CAACyB,OAAO,CAAC,cAAcuB,OAAO,KAAK,EAAE;QAAEC,EAAE,EAAE;MAAc,CAAC,CAAC;MAEhE,MAAMlD,aAAa,CAACmD,mBAAmB,CAACF,OAAO,CAAC;MAEhDhD,KAAK,CAAC8C,OAAO,CAAC,GAAGE,OAAO,yBAAyB,EAAE;QAAEC,EAAE,EAAE;MAAc,CAAC,CAAC;MACzE,MAAMpB,oBAAoB,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoBc,OAAO,GAAG,EAAEd,KAAK,CAAC;MACpDlC,KAAK,CAACkC,KAAK,CAAC,qBAAqBc,OAAO,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAc,CAAC,CAAC;IACpE,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,UAAU,GAAG,MAAOC,QAAQ,IAAK;IACrC,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChB1B,KAAK,CAACyB,OAAO,CAAC,YAAY2B,QAAQ,UAAU,EAAE;QAAEH,EAAE,EAAE;MAAiB,CAAC,CAAC;MAEvE,IAAII,MAAM;MACV,QAAQD,QAAQ;QACd,KAAK,UAAU;UACbC,MAAM,GAAG,MAAMtD,aAAa,CAACuD,YAAY,CAAC,CAAC;UAC3C;QACF,KAAK,UAAU;UACbD,MAAM,GAAG,MAAMtD,aAAa,CAACwD,YAAY,CAAC,CAAC;UAC3C;QACF,KAAK,MAAM;UACTF,MAAM,GAAG,MAAMtD,aAAa,CAACyD,QAAQ,CAAC,CAAC;UACvC;QACF;UACE,MAAM,IAAIC,KAAK,CAAC,mBAAmB,CAAC;MACxC;MAEAzD,KAAK,CAAC8C,OAAO,CAAC,GAAGM,QAAQ,8BAA8B,EAAE;QAAEH,EAAE,EAAE;MAAiB,CAAC,CAAC;MAClF,MAAMnB,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgBkB,QAAQ,QAAQ,EAAElB,KAAK,CAAC;MACtDlC,KAAK,CAACkC,KAAK,CAAC,kBAAkBkB,QAAQ,EAAE,EAAE;QAAEH,EAAE,EAAE;MAAiB,CAAC,CAAC;IACrE,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,WAAW,GAAGA,CAAC;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAS,CAAC,kBACnD3D,OAAA;IAAK4D,SAAS,EAAC,0DAA0D;IAAAD,QAAA,gBACvE3D,OAAA;MAAK4D,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnB3D,OAAA;QAAI4D,SAAS,EAAC,mCAAmC;QAAAD,QAAA,EAAEF;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9DhE,OAAA;QAAG4D,SAAS,EAAC,4BAA4B;QAAAD,QAAA,EAAED;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,EACLL,QAAQ;EAAA;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;EAED,MAAMC,WAAW,GAAGA,CAAC;IAAEC,KAAK;IAAE7B,KAAK;IAAE8B,QAAQ;IAAEC,GAAG;IAAEC,GAAG;IAAEC,IAAI,GAAG,CAAC;IAAEC,MAAM,GAAG;EAAG,CAAC,kBAC9EvE,OAAA;IAAK4D,SAAS,EAAC,mCAAmC;IAAAD,QAAA,gBAChD3D,OAAA;MAAO4D,SAAS,EAAC,mCAAmC;MAAAD,QAAA,EAAEO;IAAK;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACpEhE,OAAA;MAAK4D,SAAS,EAAC,6BAA6B;MAAAD,QAAA,gBAC1C3D,OAAA;QACEwE,IAAI,EAAC,QAAQ;QACbJ,GAAG,EAAEA,GAAI;QACTC,GAAG,EAAEA,GAAI;QACTC,IAAI,EAAEA,IAAK;QACXjC,KAAK,EAAEA,KAAM;QACb8B,QAAQ,EAAGM,CAAC,IAAKN,QAAQ,CAACO,UAAU,CAACD,CAAC,CAACE,MAAM,CAACtC,KAAK,CAAC,CAAE;QACtDuB,SAAS,EAAC;MAAiH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5H,CAAC,EACDO,MAAM,iBAAIvE,OAAA;QAAM4D,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAEY;MAAM;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMY,YAAY,GAAGA,CAAC;IAAEV,KAAK;IAAER,WAAW;IAAEmB,OAAO;IAAEV;EAAS,CAAC,kBAC7DnE,OAAA;IAAK4D,SAAS,EAAC,mCAAmC;IAAAD,QAAA,gBAChD3D,OAAA;MAAK4D,SAAS,EAAC,QAAQ;MAAAD,QAAA,gBACrB3D,OAAA;QAAO4D,SAAS,EAAC,mCAAmC;QAAAD,QAAA,EAAEO;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACnEN,WAAW,iBAAI1D,OAAA;QAAG4D,SAAS,EAAC,4BAA4B;QAAAD,QAAA,EAAED;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,eACNhE,OAAA;MACEwE,IAAI,EAAC,QAAQ;MACbM,OAAO,EAAEA,CAAA,KAAMX,QAAQ,CAAC,CAACU,OAAO,CAAE;MAClCjB,SAAS,EAAE,0NACTiB,OAAO,GAAG,aAAa,GAAG,aAAa,EACtC;MAAAlB,QAAA,eAEH3D,OAAA;QACE4D,SAAS,EAAE,8HACTiB,OAAO,GAAG,eAAe,GAAG,eAAe;MAC1C;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;EAED,MAAMe,WAAW,GAAGA,CAAC;IAAEb,KAAK;IAAE7B,KAAK;IAAE8B,QAAQ;IAAEa;EAAQ,CAAC,kBACtDhF,OAAA;IAAK4D,SAAS,EAAC,mCAAmC;IAAAD,QAAA,gBAChD3D,OAAA;MAAO4D,SAAS,EAAC,mCAAmC;MAAAD,QAAA,EAAEO;IAAK;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACpEhE,OAAA;MACEqC,KAAK,EAAEA,KAAM;MACb8B,QAAQ,EAAGM,CAAC,IAAKN,QAAQ,CAACM,CAAC,CAACE,MAAM,CAACtC,KAAK,CAAE;MAC1CuB,SAAS,EAAC,4GAA4G;MAAAD,QAAA,EAErHqB,OAAO,CAACC,GAAG,CAACC,MAAM,iBACjBlF,OAAA;QAA2BqC,KAAK,EAAE6C,MAAM,CAAC7C,KAAM;QAAAsB,QAAA,EAC5CuB,MAAM,CAAChB;MAAK,GADFgB,MAAM,CAAC7C,KAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEjB,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;EAED,oBACEhE,OAAA;IAAK4D,SAAS,EAAC,WAAW;IAAAD,QAAA,gBAExB3D,OAAA;MAAK4D,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAChD3D,OAAA;QAAA2D,QAAA,gBACE3D,OAAA;UAAI4D,SAAS,EAAC,oDAAoD;UAAAD,QAAA,gBAChE3D,OAAA,CAACb,QAAQ;YAACyE,SAAS,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iCAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhE,OAAA;UAAG4D,SAAS,EAAC,oBAAoB;UAAAD,QAAA,EAAC;QAElC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENhE,OAAA;QACE8E,OAAO,EAAEvC,kBAAmB;QAC5B4C,QAAQ,EAAE1D,MAAO;QACjBmC,SAAS,EAAC,4KAA4K;QAAAD,QAAA,GAErLlC,MAAM,gBACLzB,OAAA,CAACR,SAAS;UAACoE,SAAS,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEnDhE,OAAA,CAACT,IAAI;UAACqE,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACjC,EAAC,eAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENhE,OAAA;MAAK4D,SAAS,EAAC,uCAAuC;MAAAD,QAAA,gBAEpD3D,OAAA,CAACwD,WAAW;QACVC,KAAK,EAAC,wBAAwB;QAC9BC,WAAW,EAAC,8CAA8C;QAAAC,QAAA,eAE1D3D,OAAA;UAAK4D,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB3D,OAAA,CAACiE,WAAW;YACVC,KAAK,EAAC,kBAAkB;YACxB7B,KAAK,EAAE/B,QAAQ,CAACE,mBAAoB;YACpC2D,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,qBAAqB,EAAEE,KAAK,CAAE;YACvE+B,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,GAAI;YACTE,MAAM,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAEFhE,OAAA,CAACiE,WAAW;YACVC,KAAK,EAAC,qBAAqB;YAC3B7B,KAAK,EAAE/B,QAAQ,CAACG,kBAAmB;YACnC0D,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,oBAAoB,EAAEE,KAAK,CAAE;YACtE+B,GAAG,EAAE,GAAI;YACTC,GAAG,EAAE,IAAK;YACVC,IAAI,EAAE,IAAK;YACXC,MAAM,EAAC;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEFhE,OAAA,CAAC+E,WAAW;YACVb,KAAK,EAAC,kBAAkB;YACxB7B,KAAK,EAAE/B,QAAQ,CAACW,uBAAwB;YACxCkD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,yBAAyB,EAAEE,KAAK,CAAE;YAC3E2C,OAAO,EAAE,CACP;cAAE3C,KAAK,EAAE,QAAQ;cAAE6B,KAAK,EAAE;YAAS,CAAC,EACpC;cAAE7B,KAAK,EAAE,OAAO;cAAE6B,KAAK,EAAE;YAAQ,CAAC,EAClC;cAAE7B,KAAK,EAAE,QAAQ;cAAE6B,KAAK,EAAE;YAAS,CAAC;UACpC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdhE,OAAA,CAACwD,WAAW;QACVC,KAAK,EAAC,kBAAkB;QACxBC,WAAW,EAAC,wDAAwD;QAAAC,QAAA,eAEpE3D,OAAA;UAAK4D,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB3D,OAAA,CAACiE,WAAW;YACVC,KAAK,EAAC,mBAAmB;YACzB7B,KAAK,EAAE/B,QAAQ,CAACI,gBAAiB;YACjCyD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,kBAAkB,EAAEE,KAAK,CAAE;YACpE+B,GAAG,EAAE,GAAI;YACTC,GAAG,EAAE,GAAI;YACTC,IAAI,EAAE,GAAI;YACVC,MAAM,EAAC;UAAO;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFhE,OAAA,CAACiE,WAAW;YACVC,KAAK,EAAC,sBAAsB;YAC5B7B,KAAK,EAAE/B,QAAQ,CAACK,iBAAkB;YAClCwD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,mBAAmB,EAAEE,KAAK,CAAE;YACrE+B,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRE,MAAM,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAEFhE,OAAA,CAACiE,WAAW;YACVC,KAAK,EAAC,iBAAiB;YACvB7B,KAAK,EAAE/B,QAAQ,CAACM,YAAa;YAC7BuD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,cAAc,EAAEE,KAAK,CAAE;YAChE+B,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRE,MAAM,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAEFhE,OAAA,CAACiE,WAAW;YACVC,KAAK,EAAC,cAAc;YACpB7B,KAAK,EAAE/B,QAAQ,CAACO,eAAgB;YAChCsD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,iBAAiB,EAAEE,KAAK,CAAE;YACnE+B,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRE,MAAM,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAEFhE,OAAA,CAACiE,WAAW;YACVC,KAAK,EAAC,mBAAmB;YACzB7B,KAAK,EAAE/B,QAAQ,CAACQ,YAAa;YAC7BqD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,cAAc,EAAEE,KAAK,CAAE;YAChE+B,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRE,MAAM,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdhE,OAAA,CAACwD,WAAW;QACVC,KAAK,EAAC,qBAAqB;QAC3BC,WAAW,EAAC,uCAAuC;QAAAC,QAAA,eAEnD3D,OAAA;UAAK4D,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB3D,OAAA,CAAC4E,YAAY;YACXV,KAAK,EAAC,uBAAuB;YAC7BR,WAAW,EAAC,kDAAkD;YAC9DmB,OAAO,EAAEvE,QAAQ,CAACS,0BAA2B;YAC7CoD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,4BAA4B,EAAEE,KAAK;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAEFhE,OAAA,CAAC4E,YAAY;YACXV,KAAK,EAAC,sBAAsB;YAC5BR,WAAW,EAAC,4CAA4C;YACxDmB,OAAO,EAAEvE,QAAQ,CAACU,yBAA0B;YAC5CmD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,2BAA2B,EAAEE,KAAK;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAEFhE,OAAA,CAAC+E,WAAW;YACVb,KAAK,EAAC,sBAAsB;YAC5B7B,KAAK,EAAE/B,QAAQ,CAACY,0BAA2B;YAC3CiD,QAAQ,EAAG9B,KAAK,IAAKF,mBAAmB,CAAC,4BAA4B,EAAEE,KAAK,CAAE;YAC9E2C,OAAO,EAAE,CACP;cAAE3C,KAAK,EAAE,QAAQ;cAAE6B,KAAK,EAAE;YAAS,CAAC,EACpC;cAAE7B,KAAK,EAAE,OAAO;cAAE6B,KAAK,EAAE;YAAQ,CAAC,EAClC;cAAE7B,KAAK,EAAE,QAAQ;cAAE6B,KAAK,EAAE;YAAS,CAAC;UACpC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGdhE,OAAA,CAACwD,WAAW;QACVC,KAAK,EAAC,kBAAkB;QACxBC,WAAW,EAAC,yCAAyC;QAAAC,QAAA,EAEpDxC,eAAe,gBACdnB,OAAA;UAAK4D,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB3D,OAAA;YAAK4D,SAAS,EAAC,wBAAwB;YAAAD,QAAA,EACpCyB,MAAM,CAACC,OAAO,CAAClE,eAAe,CAACmE,gBAAgB,CAAC,CAACL,GAAG,CAAC,CAAC,CAACnC,OAAO,EAAEyC,MAAM,CAAC,kBACtEvF,OAAA;cAAmB4D,SAAS,EAAC,6DAA6D;cAAAD,QAAA,gBACxF3D,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAG4D,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EACxDb,OAAO,CAAC0C,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC;gBAAC;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACJhE,OAAA;kBAAK4D,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,GACpC4B,MAAM,CAACG,OAAO,gBACb1F,OAAA,CAACJ,WAAW;oBAACgE,SAAS,EAAC;kBAA6B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEvDhE,OAAA,CAACX,KAAK;oBAACuE,SAAS,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAChD,eACDhE,OAAA;oBAAM4D,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACpC4B,MAAM,CAACG,OAAO,GAAG,SAAS,GAAG;kBAAS;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhE,OAAA;gBACE8E,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACC,OAAO,CAAE;gBACzCqC,QAAQ,EAAE5D,OAAQ;gBAClBqC,SAAS,EAAC,uDAAuD;gBACjEH,KAAK,EAAC,kBAAkB;gBAAAE,QAAA,eAExB3D,OAAA,CAACP,IAAI;kBAACmE,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA,GAvBDlB,OAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhE,OAAA;YAAK4D,SAAS,EAAC,mCAAmC;YAAAD,QAAA,GAAC,gBACnC,EAAC,IAAIgC,IAAI,CAACxE,eAAe,CAACyE,WAAW,CAAC,CAACC,cAAc,CAAC,CAAC;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENhE,OAAA;UAAK4D,SAAS,EAAC,kBAAkB;UAAAD,QAAA,gBAC/B3D,OAAA,CAACR,SAAS;YAACoE,SAAS,EAAC;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzEhE,OAAA;YAAG4D,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGNhE,OAAA,CAACwD,WAAW;MACVC,KAAK,EAAC,gCAAgC;MACtCC,WAAW,EAAC,2EAA2E;MAAAC,QAAA,EAEtFtC,UAAU,gBACTrB,OAAA;QAAK4D,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxB3D,OAAA;UAAK4D,SAAS,EAAC,uCAAuC;UAAAD,QAAA,gBACpD3D,OAAA;YAAK4D,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxC3D,OAAA;cAAK4D,SAAS,EAAC,2DAA2D;cAAAD,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhE,OAAA;cAAK4D,SAAS,EAAC,iCAAiC;cAAAD,QAAA,GAAAxD,qBAAA,GAC7CkB,UAAU,CAACyE,wBAAwB,cAAA3F,qBAAA,uBAAnCA,qBAAA,CAAqC0F,cAAc,CAAC;YAAC;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhE,OAAA;YAAK4D,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACzC3D,OAAA;cAAK4D,SAAS,EAAC,4DAA4D;cAAAD,QAAA,EAAC;YAE5E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhE,OAAA;cAAK4D,SAAS,EAAC,kCAAkC;cAAAD,QAAA,GAAAvD,qBAAA,GAC9CiB,UAAU,CAAC0E,eAAe,cAAA3F,qBAAA,uBAA1BA,qBAAA,CAA4ByF,cAAc,CAAC;YAAC;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhE,OAAA;YAAK4D,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C3D,OAAA;cAAK4D,SAAS,EAAC,6DAA6D;cAAAD,QAAA,EAAC;YAE7E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhE,OAAA;cAAK4D,SAAS,EAAC,mCAAmC;cAAAD,QAAA,GAC/CtC,UAAU,CAAC2E,kBAAkB,EAAC,GACjC;YAAA;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhE,OAAA;YAAK4D,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C3D,OAAA;cAAK4D,SAAS,EAAC,6DAA6D;cAAAD,QAAA,EAAC;YAE7E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhE,OAAA;cAAK4D,SAAS,EAAC,mCAAmC;cAAAD,QAAA,GAAAtD,qBAAA,GAC/CgB,UAAU,CAAC4E,sBAAsB,cAAA5F,qBAAA,uBAAjCA,qBAAA,CAAmCwF,cAAc,CAAC;YAAC;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhE,OAAA;UAAK4D,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACnC3D,OAAA;YACE8E,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,UAAU,CAAE;YACtCkC,QAAQ,EAAE5D,OAAQ;YAClBqC,SAAS,EAAC,2KAA2K;YAAAD,QAAA,gBAErL3D,OAAA,CAACR,SAAS;cAACoE,SAAS,EAAE,gBAAgBrC,OAAO,GAAG,cAAc,GAAG,EAAE;YAAG;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAE3E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA;YACE8E,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,UAAU,CAAE;YACtCkC,QAAQ,EAAE5D,OAAQ;YAClBqC,SAAS,EAAC,+KAA+K;YAAAD,QAAA,gBAEzL3D,OAAA,CAACR,SAAS;cAACoE,SAAS,EAAE,gBAAgBrC,OAAO,GAAG,cAAc,GAAG,EAAE;YAAG;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAE3E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA;YACE8E,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,MAAM,CAAE;YAClCkC,QAAQ,EAAE5D,OAAQ;YAClBqC,SAAS,EAAC,mLAAmL;YAAAD,QAAA,gBAE7L3D,OAAA,CAACR,SAAS;cAACoE,SAAS,EAAE,gBAAgBrC,OAAO,GAAG,cAAc,GAAG,EAAE;YAAG;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE3E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL3C,UAAU,CAAC6E,cAAc,iBACxBlG,OAAA;UAAK4D,SAAS,EAAC,mCAAmC;UAAAD,QAAA,GAAC,aACtC,EAAC,IAAIgC,IAAI,CAACtE,UAAU,CAAC6E,cAAc,CAAC,CAACL,cAAc,CAAC,CAAC;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENhE,OAAA;QAAK4D,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/B3D,OAAA,CAACR,SAAS;UAACoE,SAAS,EAAC;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzEhE,OAAA;UAAG4D,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,eAGdhE,OAAA;MAAK4D,SAAS,EAAC,kDAAkD;MAAAD,QAAA,eAC/D3D,OAAA;QAAK4D,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/B3D,OAAA,CAACL,IAAI;UAACiE,SAAS,EAAC;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEhE,OAAA;UAAK4D,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBACpC3D,OAAA;YAAG4D,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvDhE,OAAA;YAAI4D,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAC/B3D,OAAA;cAAA2D,QAAA,EAAI;YAA4F;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrGhE,OAAA;cAAA2D,QAAA,EAAI;YAAmG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5GhE,OAAA;cAAA2D,QAAA,EAAI;YAA+E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxFhE,OAAA;cAAA2D,QAAA,EAAI;YAAkE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAneID,kBAAkB;AAAAkG,EAAA,GAAlBlG,kBAAkB;AAqexB,eAAeA,kBAAkB;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}