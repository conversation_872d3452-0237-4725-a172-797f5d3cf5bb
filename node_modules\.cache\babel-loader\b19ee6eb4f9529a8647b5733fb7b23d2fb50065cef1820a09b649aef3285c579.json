{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\Inventory.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Download, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON>dingUp, Star, <PERSON>Chart3, Brain, Eye } from 'lucide-react';\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport InventoryTable from '../components/Inventory/InventoryTable';\nimport AddInventoryModal from '../components/Inventory/AddInventoryModal';\nimport DemandForecast<PERSON><PERSON> from '../components/Predictive/DemandForecastChart';\nimport ReorderSuggestions from '../components/Predictive/ReorderSuggestions';\nimport { productsAPI, instacartAPI, predictiveAPI } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  var _marketInsights$topPr, _marketInsights$topPr2, _marketInsights$topPr3, _marketInsights$depar;\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    total: 0,\n    lowStock: 0,\n    outOfStock: 0,\n    totalValue: 0\n  });\n\n  // Market insights state\n  const [marketInsights, setMarketInsights] = useState({\n    topProducts: [],\n    departmentStats: [],\n    loading: true\n  });\n\n  // Predictive analytics state\n  const [selectedProductForecast, setSelectedProductForecast] = useState(null);\n  const [forecastLoading, setForecastLoading] = useState(false);\n  const [showPredictivePanel, setShowPredictivePanel] = useState(false);\n  const categories = [{\n    value: 'all',\n    label: 'All Categories'\n  }, {\n    value: 'Electronics',\n    label: 'Electronics'\n  }, {\n    value: 'Clothing',\n    label: 'Clothing'\n  }, {\n    value: 'Books',\n    label: 'Books'\n  }, {\n    value: 'Home & Garden',\n    label: 'Home & Garden'\n  }, {\n    value: 'Sports',\n    label: 'Sports'\n  }, {\n    value: 'Toys',\n    label: 'Toys'\n  }];\n  useEffect(() => {\n    fetchProducts();\n    fetchMarketInsights();\n  }, []);\n  const fetchMarketInsights = async () => {\n    try {\n      const [topProductsRes, departmentStatsRes] = await Promise.all([instacartAPI.getTopProducts(8), instacartAPI.getDepartmentStats()]);\n      setMarketInsights({\n        topProducts: topProductsRes.data,\n        departmentStats: departmentStatsRes.data.slice(0, 6),\n        loading: false\n      });\n    } catch (error) {\n      console.error('Failed to fetch market insights:', error);\n      setMarketInsights(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      const productsData = response.data.products || [];\n      setProducts(productsData);\n\n      // Calculate stats\n      const total = productsData.length;\n      const lowStock = productsData.filter(p => p.quantity <= p.minStock).length;\n      const outOfStock = productsData.filter(p => p.quantity === 0).length;\n      const totalValue = productsData.reduce((sum, p) => sum + p.price * p.quantity, 0);\n      setStats({\n        total,\n        lowStock,\n        outOfStock,\n        totalValue\n      });\n    } catch (error) {\n      console.error('Failed to fetch products:', error);\n      toast.error('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowAddModal(true);\n  };\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setShowAddModal(true);\n  };\n  const handleDeleteProduct = async productId => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await productsAPI.delete(productId);\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } catch (error) {\n        console.error('Failed to delete product:', error);\n        toast.error('Failed to delete product');\n      }\n    }\n  };\n  const handleModalSuccess = () => {\n    fetchProducts();\n  };\n  const handleCloseModal = () => {\n    setShowAddModal(false);\n    setEditingProduct(null);\n  };\n\n  // Predictive analytics functions\n  const handleViewForecast = async product => {\n    try {\n      setForecastLoading(true);\n      const response = await predictiveAPI.getProductForecast(product._id, {\n        days: 30\n      });\n      setSelectedProductForecast({\n        product: response.data.product,\n        forecast_info: response.data.forecast_info,\n        forecast_data: response.data.forecast_data,\n        summary: response.data.summary\n      });\n      setShowPredictivePanel(true);\n    } catch (error) {\n      console.error('Failed to fetch forecast:', error);\n      toast.error('Failed to load demand forecast');\n    } finally {\n      setForecastLoading(false);\n    }\n  };\n\n  // Filter products based on search and category\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.sku.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = filterCategory === 'all' || product.category === filterCategory;\n    return matchesSearch && matchesCategory;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Inventory Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage your product inventory and stock levels\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddProduct,\n        className: \"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-5 w-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), \"Add Product\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: stats.total\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-blue-100 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-6 w-6 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Low Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-orange-600\",\n              children: stats.lowStock\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-orange-100 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: \"h-6 w-6 text-orange-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Out of Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-red-600\",\n              children: stats.outOfStock\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-red-100 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Trash2, {\n              className: \"h-6 w-6 text-red-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-green-600\",\n              children: [\"$\", stats.totalValue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-green-100 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-6 w-6 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), !marketInsights.loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900\",\n            children: \"Market Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Product popularity and trends from market basket analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n            className: \"h-5 w-5 text-purple-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-purple-600\",\n            children: \"Live Market Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Star, {\n              className: \"h-5 w-5 text-yellow-500 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), \"Most Popular Products\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-64\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: marketInsights.topProducts.slice(0, 5),\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"product_name\",\n                  angle: -45,\n                  textAnchor: \"end\",\n                  height: 80,\n                  fontSize: 10\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"order_count\",\n                  fill: \"#8b5cf6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"h-5 w-5 text-blue-500 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), \"Category Performance\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-64\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(PieChart, {\n                children: [/*#__PURE__*/_jsxDEV(Pie, {\n                  data: marketInsights.departmentStats,\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  outerRadius: 80,\n                  dataKey: \"total_orders\",\n                  label: ({\n                    department_name,\n                    percent\n                  }) => `${department_name} ${(percent * 100).toFixed(0)}%`,\n                  children: marketInsights.departmentStats.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                    fill: `hsl(${index * 60}, 70%, 60%)`\n                  }, `cell-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-green-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                className: \"h-5 w-5 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Top Reorder Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: marketInsights.topProducts[0] ? `${(marketInsights.topProducts[0].reorder_rate * 100).toFixed(1)}%` : 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: ((_marketInsights$topPr = marketInsights.topProducts[0]) === null || _marketInsights$topPr === void 0 ? void 0 : _marketInsights$topPr.product_name) || 'No data'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Star, {\n                className: \"h-5 w-5 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Most Ordered\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: ((_marketInsights$topPr2 = marketInsights.topProducts[0]) === null || _marketInsights$topPr2 === void 0 ? void 0 : _marketInsights$topPr2.order_count.toLocaleString()) || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: ((_marketInsights$topPr3 = marketInsights.topProducts[0]) === null || _marketInsights$topPr3 === void 0 ? void 0 : _marketInsights$topPr3.product_name) || 'No data'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-purple-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(BarChart3, {\n                className: \"h-5 w-5 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Top Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: ((_marketInsights$depar = marketInsights.departmentStats[0]) === null || _marketInsights$depar === void 0 ? void 0 : _marketInsights$depar.department_name) || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: marketInsights.departmentStats[0] ? `${marketInsights.departmentStats[0].total_orders.toLocaleString()} orders` : 'No data'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Brain, {\n              className: \"h-6 w-6 text-blue-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), \"AI-Powered Inventory Intelligence\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Demand forecasting and reorder recommendations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowPredictivePanel(!showPredictivePanel),\n          className: \"inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-blue-700 bg-white hover:bg-blue-50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(Eye, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), showPredictivePanel ? 'Hide' : 'Show', \" Analytics\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), showPredictivePanel && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(ReorderSuggestions, {\n          limit: 5,\n          showActions: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: selectedProductForecast ? /*#__PURE__*/_jsxDEV(DemandForecastChart, {\n            forecastData: selectedProductForecast.forecast_data,\n            productName: selectedProductForecast.product.name,\n            currentStock: selectedProductForecast.product.quantity,\n            height: 300\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-12 w-12 mx-auto mb-3 text-gray-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"Select a product from the table to view its demand forecast\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-1\",\n              children: \"Click the forecast icon in the actions column\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1 max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(Search, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search products...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filterCategory,\n              onChange: e => setFilterCategory(e.target.value),\n              className: \"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.value,\n                children: category.label\n              }, category.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), \"Export\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(InventoryTable, {\n        products: filteredProducts,\n        loading: loading,\n        onEdit: handleEditProduct,\n        onDelete: handleDeleteProduct,\n        onRefresh: fetchProducts,\n        onViewForecast: handleViewForecast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddInventoryModal, {\n      isOpen: showAddModal,\n      onClose: handleCloseModal,\n      product: editingProduct,\n      onSuccess: handleModalSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n};\n_s(Inventory, \"Feyslur5OUNVVUtSDNApUV4pczo=\");\n_c = Inventory;\nexport default Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Plus", "Search", "Filter", "Download", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrendingUp", "Star", "BarChart3", "Brain", "Eye", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "InventoryTable", "AddInventoryModal", "DemandForecastChart", "ReorderSuggestions", "productsAPI", "instacartAPI", "predictiveAPI", "toast", "jsxDEV", "_jsxDEV", "Inventory", "_s", "_marketInsights$topPr", "_marketInsights$topPr2", "_marketInsights$topPr3", "_marketInsights$depar", "showAddModal", "setShowAddModal", "editingProduct", "setEditingProduct", "searchTerm", "setSearchTerm", "filterCategory", "setFilterCategory", "products", "setProducts", "loading", "setLoading", "stats", "setStats", "total", "lowStock", "outOfStock", "totalValue", "marketInsights", "setMarketInsights", "topProducts", "departmentStats", "selectedProductForecast", "setSelectedProductForecast", "forecastLoading", "setForecastLoading", "showPredictivePanel", "setShowPredictivePanel", "categories", "value", "label", "fetchProducts", "fetchMarketInsights", "topProductsRes", "departmentStatsRes", "Promise", "all", "getTopProducts", "getDepartmentStats", "data", "slice", "error", "console", "prev", "response", "getAll", "productsData", "length", "filter", "p", "quantity", "minStock", "reduce", "sum", "price", "handleAddProduct", "handleEditProduct", "product", "handleDeleteProduct", "productId", "window", "confirm", "delete", "success", "handleModalSuccess", "handleCloseModal", "handleViewForecast", "getProductForecast", "_id", "days", "forecast_info", "forecast_data", "summary", "filteredProducts", "matchesSearch", "name", "toLowerCase", "includes", "sku", "matchesCategory", "category", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "toLocaleString", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "angle", "textAnchor", "fontSize", "fill", "cx", "cy", "outerRadius", "department_name", "percent", "toFixed", "map", "entry", "index", "reorder_rate", "product_name", "order_count", "total_orders", "limit", "showActions", "forecastData", "productName", "currentStock", "type", "placeholder", "onChange", "e", "target", "onEdit", "onDelete", "onRefresh", "onViewForecast", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/Inventory.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Download, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON><PERSON><PERSON><PERSON>, Star, <PERSON><PERSON>hart3, <PERSON>, <PERSON> } from 'lucide-react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';\nimport InventoryTable from '../components/Inventory/InventoryTable';\nimport AddInventoryModal from '../components/Inventory/AddInventoryModal';\nimport DemandForecast<PERSON>hart from '../components/Predictive/DemandForecastChart';\nimport ReorderSuggestions from '../components/Predictive/ReorderSuggestions';\nimport { productsAPI, instacartAPI, predictiveAPI } from '../services/api';\nimport toast from 'react-hot-toast';\n\nconst Inventory = () => {\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    total: 0,\n    lowStock: 0,\n    outOfStock: 0,\n    totalValue: 0\n  });\n\n  // Market insights state\n  const [marketInsights, setMarketInsights] = useState({\n    topProducts: [],\n    departmentStats: [],\n    loading: true\n  });\n\n  // Predictive analytics state\n  const [selectedProductForecast, setSelectedProductForecast] = useState(null);\n  const [forecastLoading, setForecastLoading] = useState(false);\n  const [showPredictivePanel, setShowPredictivePanel] = useState(false);\n\n  const categories = [\n    { value: 'all', label: 'All Categories' },\n    { value: 'Electronics', label: 'Electronics' },\n    { value: 'Clothing', label: 'Clothing' },\n    { value: 'Books', label: 'Books' },\n    { value: 'Home & Garden', label: 'Home & Garden' },\n    { value: 'Sports', label: 'Sports' },\n    { value: 'Toys', label: 'Toys' }\n  ];\n\n  useEffect(() => {\n    fetchProducts();\n    fetchMarketInsights();\n  }, []);\n\n  const fetchMarketInsights = async () => {\n    try {\n      const [topProductsRes, departmentStatsRes] = await Promise.all([\n        instacartAPI.getTopProducts(8),\n        instacartAPI.getDepartmentStats()\n      ]);\n\n      setMarketInsights({\n        topProducts: topProductsRes.data,\n        departmentStats: departmentStatsRes.data.slice(0, 6),\n        loading: false\n      });\n    } catch (error) {\n      console.error('Failed to fetch market insights:', error);\n      setMarketInsights(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      const productsData = response.data.products || [];\n      setProducts(productsData);\n\n      // Calculate stats\n      const total = productsData.length;\n      const lowStock = productsData.filter(p => p.quantity <= p.minStock).length;\n      const outOfStock = productsData.filter(p => p.quantity === 0).length;\n      const totalValue = productsData.reduce((sum, p) => sum + (p.price * p.quantity), 0);\n\n      setStats({ total, lowStock, outOfStock, totalValue });\n    } catch (error) {\n      console.error('Failed to fetch products:', error);\n      toast.error('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowAddModal(true);\n  };\n\n  const handleEditProduct = (product) => {\n    setEditingProduct(product);\n    setShowAddModal(true);\n  };\n\n  const handleDeleteProduct = async (productId) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await productsAPI.delete(productId);\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } catch (error) {\n        console.error('Failed to delete product:', error);\n        toast.error('Failed to delete product');\n      }\n    }\n  };\n\n  const handleModalSuccess = () => {\n    fetchProducts();\n  };\n\n  const handleCloseModal = () => {\n    setShowAddModal(false);\n    setEditingProduct(null);\n  };\n\n  // Predictive analytics functions\n  const handleViewForecast = async (product) => {\n    try {\n      setForecastLoading(true);\n      const response = await predictiveAPI.getProductForecast(product._id, { days: 30 });\n      setSelectedProductForecast({\n        product: response.data.product,\n        forecast_info: response.data.forecast_info,\n        forecast_data: response.data.forecast_data,\n        summary: response.data.summary\n      });\n      setShowPredictivePanel(true);\n    } catch (error) {\n      console.error('Failed to fetch forecast:', error);\n      toast.error('Failed to load demand forecast');\n    } finally {\n      setForecastLoading(false);\n    }\n  };\n\n  // Filter products based on search and category\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.sku.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = filterCategory === 'all' || product.category === filterCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Inventory Management</h1>\n          <p className=\"text-gray-600\">Manage your product inventory and stock levels</p>\n        </div>\n        <button\n          onClick={handleAddProduct}\n          className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n        >\n          <Plus className=\"h-5 w-5 mr-2\" />\n          Add Product\n        </button>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Products</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{stats.total}</p>\n            </div>\n            <div className=\"p-3 bg-blue-100 rounded-full\">\n              <Filter className=\"h-6 w-6 text-blue-600\" />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-gray-600\">Low Stock</p>\n              <p className=\"text-2xl font-bold text-orange-600\">{stats.lowStock}</p>\n            </div>\n            <div className=\"p-3 bg-orange-100 rounded-full\">\n              <AlertTriangle className=\"h-6 w-6 text-orange-600\" />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-gray-600\">Out of Stock</p>\n              <p className=\"text-2xl font-bold text-red-600\">{stats.outOfStock}</p>\n            </div>\n            <div className=\"p-3 bg-red-100 rounded-full\">\n              <Trash2 className=\"h-6 w-6 text-red-600\" />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Value</p>\n              <p className=\"text-2xl font-bold text-green-600\">${stats.totalValue.toLocaleString()}</p>\n            </div>\n            <div className=\"p-3 bg-green-100 rounded-full\">\n              <Download className=\"h-6 w-6 text-green-600\" />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Market Insights Section */}\n      {!marketInsights.loading && (\n        <div className=\"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <div>\n              <h2 className=\"text-xl font-bold text-gray-900\">Market Insights</h2>\n              <p className=\"text-gray-600\">Product popularity and trends from market basket analysis</p>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <TrendingUp className=\"h-5 w-5 text-purple-600\" />\n              <span className=\"text-sm font-medium text-purple-600\">Live Market Data</span>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Top Market Products */}\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <Star className=\"h-5 w-5 text-yellow-500 mr-2\" />\n                Most Popular Products\n              </h3>\n              <div className=\"h-64\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <BarChart data={marketInsights.topProducts.slice(0, 5)}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis\n                      dataKey=\"product_name\"\n                      angle={-45}\n                      textAnchor=\"end\"\n                      height={80}\n                      fontSize={10}\n                    />\n                    <YAxis />\n                    <Tooltip />\n                    <Bar dataKey=\"order_count\" fill=\"#8b5cf6\" />\n                  </BarChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n\n            {/* Department Performance */}\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <BarChart3 className=\"h-5 w-5 text-blue-500 mr-2\" />\n                Category Performance\n              </h3>\n              <div className=\"h-64\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <PieChart>\n                    <Pie\n                      data={marketInsights.departmentStats}\n                      cx=\"50%\"\n                      cy=\"50%\"\n                      outerRadius={80}\n                      dataKey=\"total_orders\"\n                      label={({ department_name, percent }) => `${department_name} ${(percent * 100).toFixed(0)}%`}\n                    >\n                      {marketInsights.departmentStats.map((entry, index) => (\n                        <Cell key={`cell-${index}`} fill={`hsl(${index * 60}, 70%, 60%)`} />\n                      ))}\n                    </Pie>\n                    <Tooltip />\n                  </PieChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Insights */}\n          <div className=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <TrendingUp className=\"h-5 w-5 text-green-600\" />\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-600\">Top Reorder Rate</p>\n                  <p className=\"text-lg font-bold text-gray-900\">\n                    {marketInsights.topProducts[0] ? `${(marketInsights.topProducts[0].reorder_rate * 100).toFixed(1)}%` : 'N/A'}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {marketInsights.topProducts[0]?.product_name || 'No data'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Star className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-600\">Most Ordered</p>\n                  <p className=\"text-lg font-bold text-gray-900\">\n                    {marketInsights.topProducts[0]?.order_count.toLocaleString() || 'N/A'}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {marketInsights.topProducts[0]?.product_name || 'No data'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <BarChart3 className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-600\">Top Category</p>\n                  <p className=\"text-lg font-bold text-gray-900\">\n                    {marketInsights.departmentStats[0]?.department_name || 'N/A'}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {marketInsights.departmentStats[0] ? `${marketInsights.departmentStats[0].total_orders.toLocaleString()} orders` : 'No data'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Predictive Analytics Section */}\n      <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div>\n            <h2 className=\"text-xl font-bold text-gray-900 flex items-center\">\n              <Brain className=\"h-6 w-6 text-blue-600 mr-2\" />\n              AI-Powered Inventory Intelligence\n            </h2>\n            <p className=\"text-gray-600\">Demand forecasting and reorder recommendations</p>\n          </div>\n          <button\n            onClick={() => setShowPredictivePanel(!showPredictivePanel)}\n            className=\"inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-blue-700 bg-white hover:bg-blue-50 transition-colors\"\n          >\n            <Eye className=\"h-4 w-4 mr-2\" />\n            {showPredictivePanel ? 'Hide' : 'Show'} Analytics\n          </button>\n        </div>\n\n        {showPredictivePanel && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Reorder Suggestions */}\n            <ReorderSuggestions limit={5} showActions={false} />\n\n            {/* Demand Forecast */}\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              {selectedProductForecast ? (\n                <DemandForecastChart\n                  forecastData={selectedProductForecast.forecast_data}\n                  productName={selectedProductForecast.product.name}\n                  currentStock={selectedProductForecast.product.quantity}\n                  height={300}\n                />\n              ) : (\n                <div className=\"text-center py-12\">\n                  <TrendingUp className=\"h-12 w-12 mx-auto mb-3 text-gray-300\" />\n                  <p className=\"text-gray-500\">Select a product from the table to view its demand forecast</p>\n                  <p className=\"text-sm text-gray-400 mt-1\">Click the forecast icon in the actions column</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4\">\n          {/* Search */}\n          <div className=\"relative flex-1 max-w-md\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <Search className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-5 w-5 text-gray-400\" />\n              <select\n                value={filterCategory}\n                onChange={(e) => setFilterCategory(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n              >\n                {categories.map((category) => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Export Button */}\n            <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Inventory Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <InventoryTable\n          products={filteredProducts}\n          loading={loading}\n          onEdit={handleEditProduct}\n          onDelete={handleDeleteProduct}\n          onRefresh={fetchProducts}\n          onViewForecast={handleViewForecast}\n        />\n      </div>\n\n      {/* Add/Edit Product Modal */}\n      <AddInventoryModal\n        isOpen={showAddModal}\n        onClose={handleCloseModal}\n        product={editingProduct}\n        onSuccess={handleModalSuccess}\n      />\n    </div>\n  );\n};\n\nexport default Inventory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,GAAG,QAAQ,cAAc;AAC7H,SAASC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AACxH,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,iBAAiB,MAAM,2CAA2C;AACzE,OAAOC,mBAAmB,MAAM,8CAA8C;AAC9E,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,SAASC,WAAW,EAAEC,YAAY,EAAEC,aAAa,QAAQ,iBAAiB;AAC1E,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC;IACjCqD,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC;IACnD2D,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBX,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACY,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC5E,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAMmE,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACzC;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC9C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAClD;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,CACjC;EAEDpE,SAAS,CAAC,MAAM;IACdqE,aAAa,CAAC,CAAC;IACfC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAM,CAACC,cAAc,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC7D/C,YAAY,CAACgD,cAAc,CAAC,CAAC,CAAC,EAC9BhD,YAAY,CAACiD,kBAAkB,CAAC,CAAC,CAClC,CAAC;MAEFnB,iBAAiB,CAAC;QAChBC,WAAW,EAAEa,cAAc,CAACM,IAAI;QAChClB,eAAe,EAAEa,kBAAkB,CAACK,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACpD9B,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDtB,iBAAiB,CAACwB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEjC,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMqB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFpB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiC,QAAQ,GAAG,MAAMxD,WAAW,CAACyD,MAAM,CAAC,CAAC;MAC3C,MAAMC,YAAY,GAAGF,QAAQ,CAACL,IAAI,CAAC/B,QAAQ,IAAI,EAAE;MACjDC,WAAW,CAACqC,YAAY,CAAC;;MAEzB;MACA,MAAMhC,KAAK,GAAGgC,YAAY,CAACC,MAAM;MACjC,MAAMhC,QAAQ,GAAG+B,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,IAAID,CAAC,CAACE,QAAQ,CAAC,CAACJ,MAAM;MAC1E,MAAM/B,UAAU,GAAG8B,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK,CAAC,CAAC,CAACH,MAAM;MACpE,MAAM9B,UAAU,GAAG6B,YAAY,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAG,GAAIJ,CAAC,CAACK,KAAK,GAAGL,CAAC,CAACC,QAAS,EAAE,CAAC,CAAC;MAEnFrC,QAAQ,CAAC;QAAEC,KAAK;QAAEC,QAAQ;QAAEC,UAAU;QAAEC;MAAW,CAAC,CAAC;IACvD,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDlD,KAAK,CAACkD,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpD,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMuD,iBAAiB,GAAIC,OAAO,IAAK;IACrCtD,iBAAiB,CAACsD,OAAO,CAAC;IAC1BxD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyD,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAMzE,WAAW,CAAC0E,MAAM,CAACH,SAAS,CAAC;QACnCpE,KAAK,CAACwE,OAAO,CAAC,8BAA8B,CAAC;QAC7ChC,aAAa,CAAC,CAAC;MACjB,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDlD,KAAK,CAACkD,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF;EACF,CAAC;EAED,MAAMuB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjC,aAAa,CAAC,CAAC;EACjB,CAAC;EAED,MAAMkC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhE,eAAe,CAAC,KAAK,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM+D,kBAAkB,GAAG,MAAOT,OAAO,IAAK;IAC5C,IAAI;MACFhC,kBAAkB,CAAC,IAAI,CAAC;MACxB,MAAMmB,QAAQ,GAAG,MAAMtD,aAAa,CAAC6E,kBAAkB,CAACV,OAAO,CAACW,GAAG,EAAE;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC;MAClF9C,0BAA0B,CAAC;QACzBkC,OAAO,EAAEb,QAAQ,CAACL,IAAI,CAACkB,OAAO;QAC9Ba,aAAa,EAAE1B,QAAQ,CAACL,IAAI,CAAC+B,aAAa;QAC1CC,aAAa,EAAE3B,QAAQ,CAACL,IAAI,CAACgC,aAAa;QAC1CC,OAAO,EAAE5B,QAAQ,CAACL,IAAI,CAACiC;MACzB,CAAC,CAAC;MACF7C,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDlD,KAAK,CAACkD,KAAK,CAAC,gCAAgC,CAAC;IAC/C,CAAC,SAAS;MACRhB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMgD,gBAAgB,GAAGjE,QAAQ,CAACwC,MAAM,CAACS,OAAO,IAAI;IAClD,MAAMiB,aAAa,GAAGjB,OAAO,CAACkB,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzE,UAAU,CAACwE,WAAW,CAAC,CAAC,CAAC,IAC9DnB,OAAO,CAACqB,GAAG,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzE,UAAU,CAACwE,WAAW,CAAC,CAAC,CAAC;IACjF,MAAMG,eAAe,GAAGzE,cAAc,KAAK,KAAK,IAAImD,OAAO,CAACuB,QAAQ,KAAK1E,cAAc;IACvF,OAAOoE,aAAa,IAAIK,eAAe;EACzC,CAAC,CAAC;EAEF,oBACEtF,OAAA;IAAKwF,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzF,OAAA;MAAKwF,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDzF,OAAA;QAAAyF,QAAA,gBACEzF,OAAA;UAAIwF,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E7F,OAAA;UAAGwF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACN7F,OAAA;QACE8F,OAAO,EAAEhC,gBAAiB;QAC1B0B,SAAS,EAAC,gHAAgH;QAAAC,QAAA,gBAE1HzF,OAAA,CAAC9B,IAAI;UAACsH,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzF,OAAA;QAAKwF,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEzF,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzF,OAAA;YAAKwF,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzF,OAAA;cAAGwF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnE7F,OAAA;cAAGwF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEtE,KAAK,CAACE;YAAK;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CzF,OAAA,CAAC5B,MAAM;cAACoH,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7F,OAAA;QAAKwF,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEzF,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzF,OAAA;YAAKwF,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzF,OAAA;cAAGwF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9D7F,OAAA;cAAGwF,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEtE,KAAK,CAACG;YAAQ;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7CzF,OAAA,CAACzB,aAAa;cAACiH,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7F,OAAA;QAAKwF,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEzF,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzF,OAAA;YAAKwF,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzF,OAAA;cAAGwF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjE7F,OAAA;cAAGwF,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAEtE,KAAK,CAACI;YAAU;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1CzF,OAAA,CAAC1B,MAAM;cAACkH,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7F,OAAA;QAAKwF,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEzF,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzF,OAAA;YAAKwF,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzF,OAAA;cAAGwF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChE7F,OAAA;cAAGwF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,GAAC,EAACtE,KAAK,CAACK,UAAU,CAACuE,cAAc,CAAC,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,+BAA+B;YAAAC,QAAA,eAC5CzF,OAAA,CAAC3B,QAAQ;cAACmH,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAACpE,cAAc,CAACR,OAAO,iBACtBjB,OAAA;MAAKwF,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBACjGzF,OAAA;QAAKwF,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzF,OAAA;UAAAyF,QAAA,gBACEzF,OAAA;YAAIwF,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE7F,OAAA;YAAGwF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzF,OAAA,CAACxB,UAAU;YAACgH,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClD7F,OAAA;YAAMwF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7F,OAAA;QAAKwF,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDzF,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDzF,OAAA;YAAIwF,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACxEzF,OAAA,CAACvB,IAAI;cAAC+G,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7F,OAAA;YAAKwF,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBzF,OAAA,CAACb,mBAAmB;cAAC6G,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAAR,QAAA,eAC7CzF,OAAA,CAACnB,QAAQ;gBAACiE,IAAI,EAAErB,cAAc,CAACE,WAAW,CAACoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;gBAAA0C,QAAA,gBACrDzF,OAAA,CAACf,aAAa;kBAACiH,eAAe,EAAC;gBAAK;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvC7F,OAAA,CAACjB,KAAK;kBACJoH,OAAO,EAAC,cAAc;kBACtBC,KAAK,EAAE,CAAC,EAAG;kBACXC,UAAU,EAAC,KAAK;kBAChBJ,MAAM,EAAE,EAAG;kBACXK,QAAQ,EAAE;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACF7F,OAAA,CAAChB,KAAK;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACT7F,OAAA,CAACd,OAAO;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX7F,OAAA,CAAClB,GAAG;kBAACqH,OAAO,EAAC,aAAa;kBAACI,IAAI,EAAC;gBAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7F,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDzF,OAAA;YAAIwF,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACxEzF,OAAA,CAACtB,SAAS;cAAC8G,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAEtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7F,OAAA;YAAKwF,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBzF,OAAA,CAACb,mBAAmB;cAAC6G,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAAR,QAAA,eAC7CzF,OAAA,CAACZ,QAAQ;gBAAAqG,QAAA,gBACPzF,OAAA,CAACX,GAAG;kBACFyD,IAAI,EAAErB,cAAc,CAACG,eAAgB;kBACrC4E,EAAE,EAAC,KAAK;kBACRC,EAAE,EAAC,KAAK;kBACRC,WAAW,EAAE,EAAG;kBAChBP,OAAO,EAAC,cAAc;kBACtB9D,KAAK,EAAEA,CAAC;oBAAEsE,eAAe;oBAAEC;kBAAQ,CAAC,KAAK,GAAGD,eAAe,IAAI,CAACC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;kBAAApB,QAAA,EAE5FhE,cAAc,CAACG,eAAe,CAACkF,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/ChH,OAAA,CAACV,IAAI;oBAAuBiH,IAAI,EAAE,OAAOS,KAAK,GAAG,EAAE;kBAAc,GAAtD,QAAQA,KAAK,EAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAyC,CACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN7F,OAAA,CAACd,OAAO;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7F,OAAA;QAAKwF,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDzF,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDzF,OAAA;YAAKwF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzF,OAAA;cAAKwF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eAC1CzF,OAAA,CAACxB,UAAU;gBAACgH,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzF,OAAA;gBAAGwF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrE7F,OAAA;gBAAGwF,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3ChE,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,CAACF,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,CAACsF,YAAY,GAAG,GAAG,EAAEJ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;cAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC,eACJ7F,OAAA;gBAAGwF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACjC,EAAAtF,qBAAA,GAAAsB,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,cAAAxB,qBAAA,uBAA7BA,qBAAA,CAA+B+G,YAAY,KAAI;cAAS;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7F,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDzF,OAAA;YAAKwF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzF,OAAA;cAAKwF,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzCzF,OAAA,CAACvB,IAAI;gBAAC+G,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzF,OAAA;gBAAGwF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjE7F,OAAA;gBAAGwF,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3C,EAAArF,sBAAA,GAAAqB,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,cAAAvB,sBAAA,uBAA7BA,sBAAA,CAA+B+G,WAAW,CAACpB,cAAc,CAAC,CAAC,KAAI;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACJ7F,OAAA;gBAAGwF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACjC,EAAApF,sBAAA,GAAAoB,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,cAAAtB,sBAAA,uBAA7BA,sBAAA,CAA+B6G,YAAY,KAAI;cAAS;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7F,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDzF,OAAA;YAAKwF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzF,OAAA;cAAKwF,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CzF,OAAA,CAACtB,SAAS;gBAAC8G,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzF,OAAA;gBAAGwF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjE7F,OAAA;gBAAGwF,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3C,EAAAnF,qBAAA,GAAAmB,cAAc,CAACG,eAAe,CAAC,CAAC,CAAC,cAAAtB,qBAAA,uBAAjCA,qBAAA,CAAmCqG,eAAe,KAAI;cAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACJ7F,OAAA;gBAAGwF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACjChE,cAAc,CAACG,eAAe,CAAC,CAAC,CAAC,GAAG,GAAGH,cAAc,CAACG,eAAe,CAAC,CAAC,CAAC,CAACwF,YAAY,CAACrB,cAAc,CAAC,CAAC,SAAS,GAAG;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7F,OAAA;MAAKwF,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAC/FzF,OAAA;QAAKwF,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzF,OAAA;UAAAyF,QAAA,gBACEzF,OAAA;YAAIwF,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAC/DzF,OAAA,CAACrB,KAAK;cAAC6G,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qCAElD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7F,OAAA;YAAGwF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA8C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eACN7F,OAAA;UACE8F,OAAO,EAAEA,CAAA,KAAM5D,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;UAC5DuD,SAAS,EAAC,gIAAgI;UAAAC,QAAA,gBAE1IzF,OAAA,CAACpB,GAAG;YAAC4G,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC/B5D,mBAAmB,GAAG,MAAM,GAAG,MAAM,EAAC,YACzC;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL5D,mBAAmB,iBAClBjC,OAAA;QAAKwF,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDzF,OAAA,CAACN,kBAAkB;UAAC2H,KAAK,EAAE,CAAE;UAACC,WAAW,EAAE;QAAM;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGpD7F,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC/C5D,uBAAuB,gBACtB7B,OAAA,CAACP,mBAAmB;YAClB8H,YAAY,EAAE1F,uBAAuB,CAACiD,aAAc;YACpD0C,WAAW,EAAE3F,uBAAuB,CAACmC,OAAO,CAACkB,IAAK;YAClDuC,YAAY,EAAE5F,uBAAuB,CAACmC,OAAO,CAACP,QAAS;YACvDwC,MAAM,EAAE;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,gBAEF7F,OAAA;YAAKwF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzF,OAAA,CAACxB,UAAU;cAACgH,SAAS,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D7F,OAAA;cAAGwF,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA2D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5F7F,OAAA;cAAGwF,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEzF,OAAA;QAAKwF,SAAS,EAAC,kGAAkG;QAAAC,QAAA,gBAE/GzF,OAAA;UAAKwF,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCzF,OAAA;YAAKwF,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFzF,OAAA,CAAC7B,MAAM;cAACqH,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN7F,OAAA;YACE0H,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCvF,KAAK,EAAEzB,UAAW;YAClBiH,QAAQ,EAAGC,CAAC,IAAKjH,aAAa,CAACiH,CAAC,CAACC,MAAM,CAAC1F,KAAK,CAAE;YAC/CoD,SAAS,EAAC;UAAmN;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9N,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN7F,OAAA;UAAKwF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzF,OAAA;YAAKwF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzF,OAAA,CAAC5B,MAAM;cAACoH,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5C7F,OAAA;cACEoC,KAAK,EAAEvB,cAAe;cACtB+G,QAAQ,EAAGC,CAAC,IAAK/G,iBAAiB,CAAC+G,CAAC,CAACC,MAAM,CAAC1F,KAAK,CAAE;cACnDoD,SAAS,EAAC,sIAAsI;cAAAC,QAAA,EAE/ItD,UAAU,CAAC2E,GAAG,CAAEvB,QAAQ,iBACvBvF,OAAA;gBAA6BoC,KAAK,EAAEmD,QAAQ,CAACnD,KAAM;gBAAAqD,QAAA,EAChDF,QAAQ,CAAClD;cAAK,GADJkD,QAAQ,CAACnD,KAAK;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN7F,OAAA;YAAQwF,SAAS,EAAC,gIAAgI;YAAAC,QAAA,gBAChJzF,OAAA,CAAC3B,QAAQ;cAACmH,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEzF,OAAA,CAACT,cAAc;QACbwB,QAAQ,EAAEiE,gBAAiB;QAC3B/D,OAAO,EAAEA,OAAQ;QACjB8G,MAAM,EAAEhE,iBAAkB;QAC1BiE,QAAQ,EAAE/D,mBAAoB;QAC9BgE,SAAS,EAAE3F,aAAc;QACzB4F,cAAc,EAAEzD;MAAmB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN7F,OAAA,CAACR,iBAAiB;MAChB2I,MAAM,EAAE5H,YAAa;MACrB6H,OAAO,EAAE5D,gBAAiB;MAC1BR,OAAO,EAAEvD,cAAe;MACxB4H,SAAS,EAAE9D;IAAmB;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3F,EAAA,CA1bID,SAAS;AAAAqI,EAAA,GAATrI,SAAS;AA4bf,eAAeA,SAAS;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}