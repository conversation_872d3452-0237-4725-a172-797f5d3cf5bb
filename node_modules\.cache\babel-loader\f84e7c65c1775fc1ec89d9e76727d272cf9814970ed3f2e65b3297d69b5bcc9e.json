{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Package, ShoppingCart, Users, TrendingUp, AlertTriangle, DollarSign, Star, BarChart3, Brain, Clock } from 'lucide-react';\nimport StatsCard from '../components/Dashboard/StatsCard';\nimport InventoryChart from '../components/Dashboard/InventoryChart';\nimport RecentOrders from '../components/Dashboard/RecentOrders';\nimport LowStockAlert from '../components/Dashboard/LowStockAlert';\nimport ReorderSuggestions from '../components/Predictive/ReorderSuggestions';\nimport { instacartAPI, predictiveAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _predictiveSummary$da, _predictiveSummary$da2, _predictiveSummary$da3, _predictiveSummary$da4, _predictiveSummary$da5, _predictiveSummary$da6, _marketInsights$summa, _marketInsights$summa2;\n  const [marketInsights, setMarketInsights] = useState({\n    topProducts: [],\n    summary: {},\n    loading: true\n  });\n  const [predictiveSummary, setPredictiveSummary] = useState({\n    data: null,\n    loading: true\n  });\n  useEffect(() => {\n    fetchMarketInsights();\n    fetchPredictiveSummary();\n  }, []);\n  const fetchMarketInsights = async () => {\n    try {\n      const [topProductsRes, summaryRes] = await Promise.all([instacartAPI.getTopProducts(3), instacartAPI.getSummary()]);\n      setMarketInsights({\n        topProducts: topProductsRes.data,\n        summary: summaryRes.data,\n        loading: false\n      });\n    } catch (error) {\n      console.error('Failed to fetch market insights:', error);\n      setMarketInsights(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n  const fetchPredictiveSummary = async () => {\n    try {\n      const response = await predictiveAPI.getDashboardSummary();\n      setPredictiveSummary({\n        data: response.data,\n        loading: false\n      });\n    } catch (error) {\n      console.error('Failed to fetch predictive summary:', error);\n      setPredictiveSummary(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n  const stats = [{\n    title: 'Total Products',\n    value: '2,847',\n    change: '+12%',\n    changeType: 'positive',\n    icon: Package,\n    color: 'blue'\n  }, {\n    title: 'Total Orders',\n    value: '1,234',\n    change: '+8%',\n    changeType: 'positive',\n    icon: ShoppingCart,\n    color: 'green'\n  }, {\n    title: 'Active Suppliers',\n    value: '156',\n    change: '+3%',\n    changeType: 'positive',\n    icon: Users,\n    color: 'purple'\n  }, {\n    title: 'Revenue',\n    value: '$45,678',\n    change: '+15%',\n    changeType: 'positive',\n    icon: DollarSign,\n    color: 'yellow'\n  }, {\n    title: 'Active Forecasts',\n    value: ((_predictiveSummary$da = predictiveSummary.data) === null || _predictiveSummary$da === void 0 ? void 0 : (_predictiveSummary$da2 = _predictiveSummary$da.forecasts) === null || _predictiveSummary$da2 === void 0 ? void 0 : _predictiveSummary$da2.active) || '0',\n    change: 'AI Powered',\n    changeType: 'positive',\n    icon: Brain,\n    color: 'indigo'\n  }, {\n    title: 'Critical Alerts',\n    value: ((_predictiveSummary$da3 = predictiveSummary.data) === null || _predictiveSummary$da3 === void 0 ? void 0 : (_predictiveSummary$da4 = _predictiveSummary$da3.reorder_suggestions) === null || _predictiveSummary$da4 === void 0 ? void 0 : _predictiveSummary$da4.critical) || '0',\n    change: 'Immediate Action',\n    changeType: ((_predictiveSummary$da5 = predictiveSummary.data) === null || _predictiveSummary$da5 === void 0 ? void 0 : (_predictiveSummary$da6 = _predictiveSummary$da5.reorder_suggestions) === null || _predictiveSummary$da6 === void 0 ? void 0 : _predictiveSummary$da6.critical) > 0 ? 'negative' : 'positive',\n    icon: Clock,\n    color: 'red'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Welcome back! Here's what's happening with your inventory.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: [\"Last updated: \", new Date().toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(StatsCard, {\n        ...stat\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), !marketInsights.loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-6 border border-indigo-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-bold text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"h-5 w-5 text-indigo-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), \"Market Insights\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [\"Real-time market basket analysis from \", (_marketInsights$summa = marketInsights.summary.totalProducts) === null || _marketInsights$summa === void 0 ? void 0 : _marketInsights$summa.toLocaleString(), \" products\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (_marketInsights$summa2 = marketInsights.summary.totalOrders) === null || _marketInsights$summa2 === void 0 ? void 0 : _marketInsights$summa2.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Market Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-green-600\",\n              children: marketInsights.summary.totalDepartments\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: marketInsights.topProducts.map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-yellow-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Star, {\n                className: \"h-4 w-4 text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900 truncate\",\n                children: product.product_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: product.department\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-bold text-gray-900\",\n                children: product.order_count.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: \"orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 17\n          }, this)\n        }, product.product_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Inventory Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InventoryChart, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Low Stock Alerts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LowStockAlert, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(ReorderSuggestions, {\n        limit: 5,\n        showActions: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Recent Orders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RecentOrders, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"N/ouOTlbqoG4Vjzcfzf3D5nILdc=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Package", "ShoppingCart", "Users", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DollarSign", "Star", "BarChart3", "Brain", "Clock", "StatsCard", "InventoryChart", "RecentOrders", "LowStockAlert", "ReorderSuggestions", "instacartAPI", "predictiveAPI", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_predictiveSummary$da", "_predictiveSummary$da2", "_predictiveSummary$da3", "_predictiveSummary$da4", "_predictiveSummary$da5", "_predictiveSummary$da6", "_marketInsights$summa", "_marketInsights$summa2", "marketInsights", "setMarketInsights", "topProducts", "summary", "loading", "predictiveSummary", "setPredictiveSummary", "data", "fetchMarketInsights", "fetchPredictiveSummary", "topProductsRes", "summaryRes", "Promise", "all", "getTopProducts", "getSummary", "error", "console", "prev", "response", "getDashboardSummary", "stats", "title", "value", "change", "changeType", "icon", "color", "forecasts", "active", "reorder_suggestions", "critical", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "toLocaleString", "map", "stat", "index", "totalProducts", "totalOrders", "totalDepartments", "product", "product_name", "department", "order_count", "product_id", "limit", "showActions", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Package,\n  ShoppingCart,\n  Users,\n  TrendingUp,\n  AlertTriangle,\n  DollarSign,\n  Star,\n  BarChart3,\n  Brain,\n  Clock\n} from 'lucide-react';\nimport StatsCard from '../components/Dashboard/StatsCard';\nimport InventoryChart from '../components/Dashboard/InventoryChart';\nimport RecentOrders from '../components/Dashboard/RecentOrders';\nimport LowStockAlert from '../components/Dashboard/LowStockAlert';\nimport ReorderSuggestions from '../components/Predictive/ReorderSuggestions';\nimport { instacartAPI, predictiveAPI } from '../services/api';\n\nconst Dashboard = () => {\n  const [marketInsights, setMarketInsights] = useState({\n    topProducts: [],\n    summary: {},\n    loading: true\n  });\n\n  const [predictiveSummary, setPredictiveSummary] = useState({\n    data: null,\n    loading: true\n  });\n\n  useEffect(() => {\n    fetchMarketInsights();\n    fetchPredictiveSummary();\n  }, []);\n\n  const fetchMarketInsights = async () => {\n    try {\n      const [topProductsRes, summaryRes] = await Promise.all([\n        instacartAPI.getTopProducts(3),\n        instacartAPI.getSummary()\n      ]);\n\n      setMarketInsights({\n        topProducts: topProductsRes.data,\n        summary: summaryRes.data,\n        loading: false\n      });\n    } catch (error) {\n      console.error('Failed to fetch market insights:', error);\n      setMarketInsights(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  const fetchPredictiveSummary = async () => {\n    try {\n      const response = await predictiveAPI.getDashboardSummary();\n      setPredictiveSummary({\n        data: response.data,\n        loading: false\n      });\n    } catch (error) {\n      console.error('Failed to fetch predictive summary:', error);\n      setPredictiveSummary(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  const stats = [\n    {\n      title: 'Total Products',\n      value: '2,847',\n      change: '+12%',\n      changeType: 'positive',\n      icon: Package,\n      color: 'blue'\n    },\n    {\n      title: 'Total Orders',\n      value: '1,234',\n      change: '+8%',\n      changeType: 'positive',\n      icon: ShoppingCart,\n      color: 'green'\n    },\n    {\n      title: 'Active Suppliers',\n      value: '156',\n      change: '+3%',\n      changeType: 'positive',\n      icon: Users,\n      color: 'purple'\n    },\n    {\n      title: 'Revenue',\n      value: '$45,678',\n      change: '+15%',\n      changeType: 'positive',\n      icon: DollarSign,\n      color: 'yellow'\n    },\n    {\n      title: 'Active Forecasts',\n      value: predictiveSummary.data?.forecasts?.active || '0',\n      change: 'AI Powered',\n      changeType: 'positive',\n      icon: Brain,\n      color: 'indigo'\n    },\n    {\n      title: 'Critical Alerts',\n      value: predictiveSummary.data?.reorder_suggestions?.critical || '0',\n      change: 'Immediate Action',\n      changeType: predictiveSummary.data?.reorder_suggestions?.critical > 0 ? 'negative' : 'positive',\n      icon: Clock,\n      color: 'red'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n          <p className=\"text-gray-600\">Welcome back! Here's what's happening with your inventory.</p>\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          Last updated: {new Date().toLocaleString()}\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {stats.map((stat, index) => (\n          <StatsCard key={index} {...stat} />\n        ))}\n      </div>\n\n      {/* Market Insights Banner */}\n      {!marketInsights.loading && (\n        <div className=\"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-6 border border-indigo-200\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-lg font-bold text-gray-900 flex items-center\">\n                <BarChart3 className=\"h-5 w-5 text-indigo-600 mr-2\" />\n                Market Insights\n              </h2>\n              <p className=\"text-gray-600\">Real-time market basket analysis from {marketInsights.summary.totalProducts?.toLocaleString()} products</p>\n            </div>\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"text-center\">\n                <p className=\"text-2xl font-bold text-indigo-600\">{marketInsights.summary.totalOrders?.toLocaleString()}</p>\n                <p className=\"text-sm text-gray-600\">Market Orders</p>\n              </div>\n              <div className=\"text-center\">\n                <p className=\"text-2xl font-bold text-green-600\">{marketInsights.summary.totalDepartments}</p>\n                <p className=\"text-sm text-gray-600\">Categories</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Top Products Quick View */}\n          <div className=\"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {marketInsights.topProducts.map((product, index) => (\n              <div key={product.product_id} className=\"bg-white rounded-lg p-4 shadow-sm\">\n                <div className=\"flex items-center\">\n                  <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                    <Star className=\"h-4 w-4 text-yellow-600\" />\n                  </div>\n                  <div className=\"ml-3 flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 truncate\">{product.product_name}</p>\n                    <p className=\"text-xs text-gray-500\">{product.department}</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-bold text-gray-900\">{product.order_count.toLocaleString()}</p>\n                    <p className=\"text-xs text-gray-500\">orders</p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Charts and Tables */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Inventory Chart */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Inventory Overview</h3>\n          <InventoryChart />\n        </div>\n\n        {/* Low Stock Alert */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Low Stock Alerts</h3>\n          <LowStockAlert />\n        </div>\n      </div>\n\n      {/* Predictive Analytics Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* AI Reorder Suggestions */}\n        <ReorderSuggestions limit={5} showActions={false} />\n\n        {/* Recent Orders */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Orders</h3>\n          <RecentOrders />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,UAAU,EACVC,aAAa,EACbC,UAAU,EACVC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,KAAK,QACA,cAAc;AACrB,OAAOC,SAAS,MAAM,mCAAmC;AACzD,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,SAASC,YAAY,EAAEC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACtB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC;IACnDiC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,CAAC,CAAC;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC;IACzDsC,IAAI,EAAE,IAAI;IACVH,OAAO,EAAE;EACX,CAAC,CAAC;EAEFlC,SAAS,CAAC,MAAM;IACdsC,mBAAmB,CAAC,CAAC;IACrBC,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAM,CAACE,cAAc,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACrD3B,YAAY,CAAC4B,cAAc,CAAC,CAAC,CAAC,EAC9B5B,YAAY,CAAC6B,UAAU,CAAC,CAAC,CAC1B,CAAC;MAEFd,iBAAiB,CAAC;QAChBC,WAAW,EAAEQ,cAAc,CAACH,IAAI;QAChCJ,OAAO,EAAEQ,UAAU,CAACJ,IAAI;QACxBH,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDf,iBAAiB,CAACiB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEd,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMK,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMhC,aAAa,CAACiC,mBAAmB,CAAC,CAAC;MAC1Dd,oBAAoB,CAAC;QACnBC,IAAI,EAAEY,QAAQ,CAACZ,IAAI;QACnBH,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DV,oBAAoB,CAACY,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEd,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAC7D;EACF,CAAC;EAED,MAAMiB,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEvD,OAAO;IACbwD,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEtD,YAAY;IAClBuD,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAErD,KAAK;IACXsD,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAElD,UAAU;IAChBmD,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,EAAA/B,qBAAA,GAAAa,iBAAiB,CAACE,IAAI,cAAAf,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBoC,SAAS,cAAAnC,sBAAA,uBAAjCA,sBAAA,CAAmCoC,MAAM,KAAI,GAAG;IACvDL,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAE/C,KAAK;IACXgD,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,EAAA7B,sBAAA,GAAAW,iBAAiB,CAACE,IAAI,cAAAb,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAwBoC,mBAAmB,cAAAnC,sBAAA,uBAA3CA,sBAAA,CAA6CoC,QAAQ,KAAI,GAAG;IACnEP,MAAM,EAAE,kBAAkB;IAC1BC,UAAU,EAAE,EAAA7B,sBAAA,GAAAS,iBAAiB,CAACE,IAAI,cAAAX,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAwBkC,mBAAmB,cAAAjC,sBAAA,uBAA3CA,sBAAA,CAA6CkC,QAAQ,IAAG,CAAC,GAAG,UAAU,GAAG,UAAU;IAC/FL,IAAI,EAAE9C,KAAK;IACX+C,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEtC,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5C,OAAA;MAAK2C,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD5C,OAAA;QAAA4C,QAAA,gBACE5C,OAAA;UAAI2C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DhD,OAAA;UAAG2C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC,eACNhD,OAAA;QAAK2C,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,gBACvB,EAAC,IAAIK,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAK2C,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEZ,KAAK,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBrD,OAAA,CAACR,SAAS;QAAA,GAAiB4D;MAAI,GAAfC,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL,CAACrC,cAAc,CAACI,OAAO,iBACtBf,OAAA;MAAK2C,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBACjG5C,OAAA;QAAK2C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5C,OAAA;UAAA4C,QAAA,gBACE5C,OAAA;YAAI2C,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAC/D5C,OAAA,CAACX,SAAS;cAACsD,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAExD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhD,OAAA;YAAG2C,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,wCAAsC,GAAAnC,qBAAA,GAACE,cAAc,CAACG,OAAO,CAACwC,aAAa,cAAA7C,qBAAA,uBAApCA,qBAAA,CAAsCyC,cAAc,CAAC,CAAC,EAAC,WAAS;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrI,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5C,OAAA;YAAK2C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5C,OAAA;cAAG2C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAAlC,sBAAA,GAAEC,cAAc,CAACG,OAAO,CAACyC,WAAW,cAAA7C,sBAAA,uBAAlCA,sBAAA,CAAoCwC,cAAc,CAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5GhD,OAAA;cAAG2C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5C,OAAA;cAAG2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEjC,cAAc,CAACG,OAAO,CAAC0C;YAAgB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9FhD,OAAA;cAAG2C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,4CAA4C;QAAAC,QAAA,EACxDjC,cAAc,CAACE,WAAW,CAACsC,GAAG,CAAC,CAACM,OAAO,EAAEJ,KAAK,kBAC7CrD,OAAA;UAA8B2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eACzE5C,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC5C,OAAA;cAAK2C,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C5C,OAAA,CAACZ,IAAI;gBAACuD,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNhD,OAAA;cAAK2C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5C,OAAA;gBAAG2C,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAEa,OAAO,CAACC;cAAY;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFhD,OAAA;gBAAG2C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEa,OAAO,CAACE;cAAU;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNhD,OAAA;cAAK2C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5C,OAAA;gBAAG2C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAEa,OAAO,CAACG,WAAW,CAACV,cAAc,CAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzFhD,OAAA;gBAAG2C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAbES,OAAO,CAACI,UAAU;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcvB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhD,OAAA;MAAK2C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD5C,OAAA;QAAK2C,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFhD,OAAA,CAACP,cAAc;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EhD,OAAA,CAACL,aAAa;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAK2C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD5C,OAAA,CAACJ,kBAAkB;QAACkE,KAAK,EAAE,CAAE;QAACC,WAAW,EAAE;MAAM;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpDhD,OAAA;QAAK2C,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EhD,OAAA,CAACN,YAAY;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAjMID,SAAS;AAAA+D,EAAA,GAAT/D,SAAS;AAmMf,eAAeA,SAAS;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}