{"name": "inventory-management-backend", "version": "1.0.0", "description": "Backend API for Inventory Management Dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "npx nodemon server.js", "test": "jest", "seed": "node scripts/seedData.js", "create-admin": "node scripts/createAdmin.js", "import-instacart": "node scripts/importInstacartData.js"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-validator": "^6.14.3", "helmet": "^6.0.1", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.0", "morgan": "^1.10.0", "multer": "^1.4.4", "node-cron": "^4.2.1"}, "devDependencies": {"jest": "^29.4.0", "nodemon": "^2.0.20", "supertest": "^6.3.3"}, "keywords": ["inventory", "management", "dashboard", "mongodb", "express", "api"], "author": "Your Name", "license": "MIT"}