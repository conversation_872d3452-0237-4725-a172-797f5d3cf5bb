{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\components\\\\Predictive\\\\DemandForecastChart.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Area, ComposedChart, Bar } from 'recharts';\nimport { Calendar, TrendingUp, AlertTriangle, Info } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DemandForecastChart = ({\n  forecastData,\n  productName,\n  currentStock = 0,\n  showConfidenceInterval = true,\n  height = 400\n}) => {\n  _s();\n  var _summary$totalPredict, _summary$totalPredict2;\n  const [chartData, setChartData] = useState([]);\n  const [summary, setSummary] = useState({});\n  useEffect(() => {\n    if (forecastData && forecastData.length > 0) {\n      processChartData();\n    }\n  }, [forecastData, currentStock]);\n  const processChartData = () => {\n    const processedData = forecastData.map(point => ({\n      date: new Date(point.date).toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      }),\n      fullDate: point.date,\n      predicted: Math.round(point.predicted_demand),\n      lowerBound: showConfidenceInterval ? Math.round(point.lower_bound) : null,\n      upperBound: showConfidenceInterval ? Math.round(point.upper_bound) : null,\n      actual: point.actual_demand || null,\n      stockLevel: Math.max(0, currentStock - point.predicted_demand)\n    }));\n    setChartData(processedData);\n\n    // Calculate summary statistics\n    const totalPredicted = processedData.reduce((sum, point) => sum + point.predicted, 0);\n    const avgDaily = totalPredicted / processedData.length;\n    const maxDemand = Math.max(...processedData.map(p => p.predicted));\n    const minDemand = Math.min(...processedData.map(p => p.predicted));\n    setSummary({\n      totalPredicted: Math.round(totalPredicted),\n      avgDaily: Math.round(avgDaily * 10) / 10,\n      maxDemand: Math.round(maxDemand),\n      minDemand: Math.round(minDemand),\n      forecastDays: processedData.length\n    });\n  };\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-medium text-gray-900\",\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: entry.color\n          },\n          className: \"text-sm\",\n          children: [entry.name, \": \", entry.value, \" units\"]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const getStockStatusColor = stockLevel => {\n    if (stockLevel <= 0) return '#ef4444'; // Red - Out of stock\n    if (stockLevel <= 10) return '#f59e0b'; // Amber - Low stock\n    return '#10b981'; // Green - Good stock\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n            className: \"h-5 w-5 mr-2 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), \"Demand Forecast\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), productName && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600 mt-1\",\n          children: productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 text-sm text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [summary.forecastDays, \" days\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 rounded-lg p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium text-blue-600 uppercase tracking-wide\",\n          children: \"Total Predicted\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-blue-900\",\n          children: [(_summary$totalPredict = summary.totalPredicted) === null || _summary$totalPredict === void 0 ? void 0 : _summary$totalPredict.toLocaleString(), \" units\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-50 rounded-lg p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium text-green-600 uppercase tracking-wide\",\n          children: \"Avg Daily\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-green-900\",\n          children: [summary.avgDaily, \" units\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-purple-50 rounded-lg p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium text-purple-600 uppercase tracking-wide\",\n          children: \"Peak Demand\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-purple-900\",\n          children: [summary.maxDemand, \" units\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium text-gray-600 uppercase tracking-wide\",\n          children: \"Current Stock\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-gray-900\",\n          children: [currentStock === null || currentStock === void 0 ? void 0 : currentStock.toLocaleString(), \" units\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: height\n      },\n      children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n        width: \"100%\",\n        height: \"100%\",\n        children: /*#__PURE__*/_jsxDEV(ComposedChart, {\n          data: chartData,\n          margin: {\n            top: 20,\n            right: 30,\n            left: 20,\n            bottom: 5\n          },\n          children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n            strokeDasharray: \"3 3\",\n            stroke: \"#f0f0f0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n            dataKey: \"date\",\n            tick: {\n              fontSize: 12\n            },\n            stroke: \"#6b7280\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n            tick: {\n              fontSize: 12\n            },\n            stroke: \"#6b7280\",\n            label: {\n              value: 'Units',\n              angle: -90,\n              position: 'insideLeft'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 31\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), showConfidenceInterval && /*#__PURE__*/_jsxDEV(Area, {\n            type: \"monotone\",\n            dataKey: \"upperBound\",\n            stackId: \"1\",\n            stroke: \"none\",\n            fill: \"#dbeafe\",\n            fillOpacity: 0.3,\n            name: \"Confidence Interval\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), showConfidenceInterval && /*#__PURE__*/_jsxDEV(Area, {\n            type: \"monotone\",\n            dataKey: \"lowerBound\",\n            stackId: \"1\",\n            stroke: \"none\",\n            fill: \"#ffffff\",\n            fillOpacity: 1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Line, {\n            type: \"monotone\",\n            dataKey: \"predicted\",\n            stroke: \"#3b82f6\",\n            strokeWidth: 3,\n            dot: {\n              fill: '#3b82f6',\n              strokeWidth: 2,\n              r: 4\n            },\n            name: \"Predicted Demand\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Line, {\n            type: \"monotone\",\n            dataKey: \"actual\",\n            stroke: \"#10b981\",\n            strokeWidth: 2,\n            strokeDasharray: \"5 5\",\n            dot: {\n              fill: '#10b981',\n              strokeWidth: 2,\n              r: 3\n            },\n            name: \"Actual Demand\",\n            connectNulls: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Bar, {\n            dataKey: \"stockLevel\",\n            fill: entry => getStockStatusColor(entry.stockLevel),\n            fillOpacity: 0.3,\n            name: \"Projected Stock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(Info, {\n          className: \"h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium mb-1\",\n            children: \"Forecast Insights:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\u2022 Expected demand over \", summary.forecastDays, \" days: \", (_summary$totalPredict2 = summary.totalPredicted) === null || _summary$totalPredict2 === void 0 ? void 0 : _summary$totalPredict2.toLocaleString(), \" units\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\u2022 Average daily demand: \", summary.avgDaily, \" units\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\u2022 Peak demand day: \", summary.maxDemand, \" units\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), currentStock > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\u2022 Current stock will last approximately \", Math.floor(currentStock / (summary.avgDaily || 1)), \" days at average demand\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), currentStock < summary.avgDaily * 7 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n          className: \"h-5 w-5 text-amber-600 mt-0.5 mr-2 flex-shrink-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium text-amber-800\",\n            children: \"Low Stock Warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-amber-700 mt-1\",\n            children: \"Current stock level may not be sufficient for the next 7 days based on predicted demand. Consider placing a reorder soon.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(DemandForecastChart, \"UKc5OtetJ+p8oRyyIsGTrGeA8F8=\");\n_c = DemandForecastChart;\nexport default DemandForecastChart;\nvar _c;\n$RefreshReg$(_c, \"DemandForecastChart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "Area", "ComposedChart", "Bar", "Calendar", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "jsxDEV", "_jsxDEV", "DemandForecastChart", "forecastData", "productName", "currentStock", "showConfidenceInterval", "height", "_s", "_summary$totalPredict", "_summary$totalPredict2", "chartData", "setChartData", "summary", "set<PERSON>ummary", "length", "processChartData", "processedData", "map", "point", "date", "Date", "toLocaleDateString", "month", "day", "fullDate", "predicted", "Math", "round", "predicted_demand", "lowerBound", "lower_bound", "upperBound", "upper_bound", "actual", "actual_demand", "stockLevel", "max", "totalPredicted", "reduce", "sum", "avgDaily", "max<PERSON><PERSON><PERSON>", "p", "<PERSON><PERSON><PERSON><PERSON>", "min", "forecastDays", "CustomTooltip", "active", "payload", "label", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "index", "style", "color", "name", "value", "getStockStatusColor", "toLocaleString", "width", "data", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stroke", "dataKey", "tick", "fontSize", "angle", "position", "content", "type", "stackId", "fill", "fillOpacity", "strokeWidth", "dot", "r", "connectNulls", "floor", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/components/Predictive/DemandForecastChart.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>hart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  Area,\n  ComposedChart,\n  Bar\n} from 'recharts';\nimport { Calendar, TrendingUp, AlertTriangle, Info } from 'lucide-react';\n\nconst DemandForecastChart = ({ \n  forecastData, \n  productName, \n  currentStock = 0,\n  showConfidenceInterval = true,\n  height = 400 \n}) => {\n  const [chartData, setChartData] = useState([]);\n  const [summary, setSummary] = useState({});\n\n  useEffect(() => {\n    if (forecastData && forecastData.length > 0) {\n      processChartData();\n    }\n  }, [forecastData, currentStock]);\n\n  const processChartData = () => {\n    const processedData = forecastData.map(point => ({\n      date: new Date(point.date).toLocaleDateString('en-US', { \n        month: 'short', \n        day: 'numeric' \n      }),\n      fullDate: point.date,\n      predicted: Math.round(point.predicted_demand),\n      lowerBound: showConfidenceInterval ? Math.round(point.lower_bound) : null,\n      upperBound: showConfidenceInterval ? Math.round(point.upper_bound) : null,\n      actual: point.actual_demand || null,\n      stockLevel: Math.max(0, currentStock - point.predicted_demand)\n    }));\n\n    setChartData(processedData);\n\n    // Calculate summary statistics\n    const totalPredicted = processedData.reduce((sum, point) => sum + point.predicted, 0);\n    const avgDaily = totalPredicted / processedData.length;\n    const maxDemand = Math.max(...processedData.map(p => p.predicted));\n    const minDemand = Math.min(...processedData.map(p => p.predicted));\n\n    setSummary({\n      totalPredicted: Math.round(totalPredicted),\n      avgDaily: Math.round(avgDaily * 10) / 10,\n      maxDemand: Math.round(maxDemand),\n      minDemand: Math.round(minDemand),\n      forecastDays: processedData.length\n    });\n  };\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n          <p className=\"font-medium text-gray-900\">{label}</p>\n          {payload.map((entry, index) => (\n            <p key={index} style={{ color: entry.color }} className=\"text-sm\">\n              {entry.name}: {entry.value} units\n            </p>\n          ))}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  const getStockStatusColor = (stockLevel) => {\n    if (stockLevel <= 0) return '#ef4444'; // Red - Out of stock\n    if (stockLevel <= 10) return '#f59e0b'; // Amber - Low stock\n    return '#10b981'; // Green - Good stock\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n            <TrendingUp className=\"h-5 w-5 mr-2 text-blue-600\" />\n            Demand Forecast\n          </h3>\n          {productName && (\n            <p className=\"text-sm text-gray-600 mt-1\">{productName}</p>\n          )}\n        </div>\n        <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n          <Calendar className=\"h-4 w-4\" />\n          <span>{summary.forecastDays} days</span>\n        </div>\n      </div>\n\n      {/* Summary Cards */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n        <div className=\"bg-blue-50 rounded-lg p-3\">\n          <div className=\"text-xs font-medium text-blue-600 uppercase tracking-wide\">\n            Total Predicted\n          </div>\n          <div className=\"text-lg font-bold text-blue-900\">\n            {summary.totalPredicted?.toLocaleString()} units\n          </div>\n        </div>\n        <div className=\"bg-green-50 rounded-lg p-3\">\n          <div className=\"text-xs font-medium text-green-600 uppercase tracking-wide\">\n            Avg Daily\n          </div>\n          <div className=\"text-lg font-bold text-green-900\">\n            {summary.avgDaily} units\n          </div>\n        </div>\n        <div className=\"bg-purple-50 rounded-lg p-3\">\n          <div className=\"text-xs font-medium text-purple-600 uppercase tracking-wide\">\n            Peak Demand\n          </div>\n          <div className=\"text-lg font-bold text-purple-900\">\n            {summary.maxDemand} units\n          </div>\n        </div>\n        <div className=\"bg-gray-50 rounded-lg p-3\">\n          <div className=\"text-xs font-medium text-gray-600 uppercase tracking-wide\">\n            Current Stock\n          </div>\n          <div className=\"text-lg font-bold text-gray-900\">\n            {currentStock?.toLocaleString()} units\n          </div>\n        </div>\n      </div>\n\n      {/* Chart */}\n      <div style={{ height: height }}>\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n            <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n            <XAxis \n              dataKey=\"date\" \n              tick={{ fontSize: 12 }}\n              stroke=\"#6b7280\"\n            />\n            <YAxis \n              tick={{ fontSize: 12 }}\n              stroke=\"#6b7280\"\n              label={{ value: 'Units', angle: -90, position: 'insideLeft' }}\n            />\n            <Tooltip content={<CustomTooltip />} />\n            <Legend />\n            \n            {/* Confidence Interval Area */}\n            {showConfidenceInterval && (\n              <Area\n                type=\"monotone\"\n                dataKey=\"upperBound\"\n                stackId=\"1\"\n                stroke=\"none\"\n                fill=\"#dbeafe\"\n                fillOpacity={0.3}\n                name=\"Confidence Interval\"\n              />\n            )}\n            {showConfidenceInterval && (\n              <Area\n                type=\"monotone\"\n                dataKey=\"lowerBound\"\n                stackId=\"1\"\n                stroke=\"none\"\n                fill=\"#ffffff\"\n                fillOpacity={1}\n              />\n            )}\n            \n            {/* Predicted Demand Line */}\n            <Line\n              type=\"monotone\"\n              dataKey=\"predicted\"\n              stroke=\"#3b82f6\"\n              strokeWidth={3}\n              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}\n              name=\"Predicted Demand\"\n            />\n            \n            {/* Actual Demand Line (if available) */}\n            <Line\n              type=\"monotone\"\n              dataKey=\"actual\"\n              stroke=\"#10b981\"\n              strokeWidth={2}\n              strokeDasharray=\"5 5\"\n              dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}\n              name=\"Actual Demand\"\n              connectNulls={false}\n            />\n            \n            {/* Stock Level Bar */}\n            <Bar\n              dataKey=\"stockLevel\"\n              fill={(entry) => getStockStatusColor(entry.stockLevel)}\n              fillOpacity={0.3}\n              name=\"Projected Stock\"\n            />\n          </ComposedChart>\n        </ResponsiveContainer>\n      </div>\n\n      {/* Insights */}\n      <div className=\"mt-4 p-4 bg-gray-50 rounded-lg\">\n        <div className=\"flex items-start\">\n          <Info className=\"h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0\" />\n          <div className=\"text-sm text-gray-700\">\n            <p className=\"font-medium mb-1\">Forecast Insights:</p>\n            <ul className=\"space-y-1 text-xs\">\n              <li>• Expected demand over {summary.forecastDays} days: {summary.totalPredicted?.toLocaleString()} units</li>\n              <li>• Average daily demand: {summary.avgDaily} units</li>\n              <li>• Peak demand day: {summary.maxDemand} units</li>\n              {currentStock > 0 && (\n                <li>• Current stock will last approximately {Math.floor(currentStock / (summary.avgDaily || 1))} days at average demand</li>\n              )}\n            </ul>\n          </div>\n        </div>\n      </div>\n\n      {/* Stock Alert */}\n      {currentStock < summary.avgDaily * 7 && (\n        <div className=\"mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg\">\n          <div className=\"flex items-start\">\n            <AlertTriangle className=\"h-5 w-5 text-amber-600 mt-0.5 mr-2 flex-shrink-0\" />\n            <div className=\"text-sm\">\n              <p className=\"font-medium text-amber-800\">Low Stock Warning</p>\n              <p className=\"text-amber-700 mt-1\">\n                Current stock level may not be sufficient for the next 7 days based on predicted demand.\n                Consider placing a reorder soon.\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DemandForecastChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,IAAI,EACJC,aAAa,EACbC,GAAG,QACE,UAAU;AACjB,SAASC,QAAQ,EAAEC,UAAU,EAAEC,aAAa,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,YAAY;EACZC,WAAW;EACXC,YAAY,GAAG,CAAC;EAChBC,sBAAsB,GAAG,IAAI;EAC7BC,MAAM,GAAG;AACX,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACd,IAAImB,YAAY,IAAIA,YAAY,CAACY,MAAM,GAAG,CAAC,EAAE;MAC3CC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACb,YAAY,EAAEE,YAAY,CAAC,CAAC;EAEhC,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,aAAa,GAAGd,YAAY,CAACe,GAAG,CAACC,KAAK,KAAK;MAC/CC,IAAI,EAAE,IAAIC,IAAI,CAACF,KAAK,CAACC,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACrDC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;MACFC,QAAQ,EAAEN,KAAK,CAACC,IAAI;MACpBM,SAAS,EAAEC,IAAI,CAACC,KAAK,CAACT,KAAK,CAACU,gBAAgB,CAAC;MAC7CC,UAAU,EAAExB,sBAAsB,GAAGqB,IAAI,CAACC,KAAK,CAACT,KAAK,CAACY,WAAW,CAAC,GAAG,IAAI;MACzEC,UAAU,EAAE1B,sBAAsB,GAAGqB,IAAI,CAACC,KAAK,CAACT,KAAK,CAACc,WAAW,CAAC,GAAG,IAAI;MACzEC,MAAM,EAAEf,KAAK,CAACgB,aAAa,IAAI,IAAI;MACnCC,UAAU,EAAET,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEhC,YAAY,GAAGc,KAAK,CAACU,gBAAgB;IAC/D,CAAC,CAAC,CAAC;IAEHjB,YAAY,CAACK,aAAa,CAAC;;IAE3B;IACA,MAAMqB,cAAc,GAAGrB,aAAa,CAACsB,MAAM,CAAC,CAACC,GAAG,EAAErB,KAAK,KAAKqB,GAAG,GAAGrB,KAAK,CAACO,SAAS,EAAE,CAAC,CAAC;IACrF,MAAMe,QAAQ,GAAGH,cAAc,GAAGrB,aAAa,CAACF,MAAM;IACtD,MAAM2B,SAAS,GAAGf,IAAI,CAACU,GAAG,CAAC,GAAGpB,aAAa,CAACC,GAAG,CAACyB,CAAC,IAAIA,CAAC,CAACjB,SAAS,CAAC,CAAC;IAClE,MAAMkB,SAAS,GAAGjB,IAAI,CAACkB,GAAG,CAAC,GAAG5B,aAAa,CAACC,GAAG,CAACyB,CAAC,IAAIA,CAAC,CAACjB,SAAS,CAAC,CAAC;IAElEZ,UAAU,CAAC;MACTwB,cAAc,EAAEX,IAAI,CAACC,KAAK,CAACU,cAAc,CAAC;MAC1CG,QAAQ,EAAEd,IAAI,CAACC,KAAK,CAACa,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE;MACxCC,SAAS,EAAEf,IAAI,CAACC,KAAK,CAACc,SAAS,CAAC;MAChCE,SAAS,EAAEjB,IAAI,CAACC,KAAK,CAACgB,SAAS,CAAC;MAChCE,YAAY,EAAE7B,aAAa,CAACF;IAC9B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgC,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAAClC,MAAM,EAAE;MACvC,oBACEd,OAAA;QAAKkD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEnD,OAAA;UAAGkD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEF;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACnDP,OAAO,CAAC/B,GAAG,CAAC,CAACuC,KAAK,EAAEC,KAAK,kBACxBzD,OAAA;UAAe0D,KAAK,EAAE;YAAEC,KAAK,EAAEH,KAAK,CAACG;UAAM,CAAE;UAACT,SAAS,EAAC,SAAS;UAAAC,QAAA,GAC9DK,KAAK,CAACI,IAAI,EAAC,IAAE,EAACJ,KAAK,CAACK,KAAK,EAAC,QAC7B;QAAA,GAFQJ,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMO,mBAAmB,GAAI3B,UAAU,IAAK;IAC1C,IAAIA,UAAU,IAAI,CAAC,EAAE,OAAO,SAAS,CAAC,CAAC;IACvC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACxC,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;EAED,oBACEnC,OAAA;IAAKkD,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBAEvEnD,OAAA;MAAKkD,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDnD,OAAA;QAAAmD,QAAA,gBACEnD,OAAA;UAAIkD,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACnEnD,OAAA,CAACJ,UAAU;YAACsD,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACJpD,WAAW,iBACVH,OAAA;UAAGkD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEhD;QAAW;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC3D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEnD,OAAA,CAACL,QAAQ;UAACuD,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChCvD,OAAA;UAAAmD,QAAA,GAAOvC,OAAO,CAACiC,YAAY,EAAC,OAAK;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA;MAAKkD,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACzDnD,OAAA;QAAKkD,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCnD,OAAA;UAAKkD,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAE3E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,iCAAiC;UAAAC,QAAA,IAAA3C,qBAAA,GAC7CI,OAAO,CAACyB,cAAc,cAAA7B,qBAAA,uBAAtBA,qBAAA,CAAwBuD,cAAc,CAAC,CAAC,EAAC,QAC5C;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCnD,OAAA;UAAKkD,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAAC;QAE5E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,GAC9CvC,OAAO,CAAC4B,QAAQ,EAAC,QACpB;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CnD,OAAA;UAAKkD,SAAS,EAAC,6DAA6D;UAAAC,QAAA,EAAC;QAE7E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAC/CvC,OAAO,CAAC6B,SAAS,EAAC,QACrB;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCnD,OAAA;UAAKkD,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAE3E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,iCAAiC;UAAAC,QAAA,GAC7C/C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE2D,cAAc,CAAC,CAAC,EAAC,QAClC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA;MAAK0D,KAAK,EAAE;QAAEpD,MAAM,EAAEA;MAAO,CAAE;MAAA6C,QAAA,eAC7BnD,OAAA,CAACT,mBAAmB;QAACyE,KAAK,EAAC,MAAM;QAAC1D,MAAM,EAAC,MAAM;QAAA6C,QAAA,eAC7CnD,OAAA,CAACP,aAAa;UAACwE,IAAI,EAAEvD,SAAU;UAACwD,MAAM,EAAE;YAAEC,GAAG,EAAE,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,IAAI,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBAClFnD,OAAA,CAACZ,aAAa;YAACmF,eAAe,EAAC,KAAK;YAACC,MAAM,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDvD,OAAA,CAACd,KAAK;YACJuF,OAAO,EAAC,MAAM;YACdC,IAAI,EAAE;cAAEC,QAAQ,EAAE;YAAG,CAAE;YACvBH,MAAM,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFvD,OAAA,CAACb,KAAK;YACJuF,IAAI,EAAE;cAAEC,QAAQ,EAAE;YAAG,CAAE;YACvBH,MAAM,EAAC,SAAS;YAChBvB,KAAK,EAAE;cAAEY,KAAK,EAAE,OAAO;cAAEe,KAAK,EAAE,CAAC,EAAE;cAAEC,QAAQ,EAAE;YAAa;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACFvD,OAAA,CAACX,OAAO;YAACyF,OAAO,eAAE9E,OAAA,CAAC8C,aAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCvD,OAAA,CAACV,MAAM;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAGTlD,sBAAsB,iBACrBL,OAAA,CAACR,IAAI;YACHuF,IAAI,EAAC,UAAU;YACfN,OAAO,EAAC,YAAY;YACpBO,OAAO,EAAC,GAAG;YACXR,MAAM,EAAC,MAAM;YACbS,IAAI,EAAC,SAAS;YACdC,WAAW,EAAE,GAAI;YACjBtB,IAAI,EAAC;UAAqB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACF,EACAlD,sBAAsB,iBACrBL,OAAA,CAACR,IAAI;YACHuF,IAAI,EAAC,UAAU;YACfN,OAAO,EAAC,YAAY;YACpBO,OAAO,EAAC,GAAG;YACXR,MAAM,EAAC,MAAM;YACbS,IAAI,EAAC,SAAS;YACdC,WAAW,EAAE;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CACF,eAGDvD,OAAA,CAACf,IAAI;YACH8F,IAAI,EAAC,UAAU;YACfN,OAAO,EAAC,WAAW;YACnBD,MAAM,EAAC,SAAS;YAChBW,WAAW,EAAE,CAAE;YACfC,GAAG,EAAE;cAAEH,IAAI,EAAE,SAAS;cAAEE,WAAW,EAAE,CAAC;cAAEE,CAAC,EAAE;YAAE,CAAE;YAC/CzB,IAAI,EAAC;UAAkB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAGFvD,OAAA,CAACf,IAAI;YACH8F,IAAI,EAAC,UAAU;YACfN,OAAO,EAAC,QAAQ;YAChBD,MAAM,EAAC,SAAS;YAChBW,WAAW,EAAE,CAAE;YACfZ,eAAe,EAAC,KAAK;YACrBa,GAAG,EAAE;cAAEH,IAAI,EAAE,SAAS;cAAEE,WAAW,EAAE,CAAC;cAAEE,CAAC,EAAE;YAAE,CAAE;YAC/CzB,IAAI,EAAC,eAAe;YACpB0B,YAAY,EAAE;UAAM;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAGFvD,OAAA,CAACN,GAAG;YACF+E,OAAO,EAAC,YAAY;YACpBQ,IAAI,EAAGzB,KAAK,IAAKM,mBAAmB,CAACN,KAAK,CAACrB,UAAU,CAAE;YACvD+C,WAAW,EAAE,GAAI;YACjBtB,IAAI,EAAC;UAAiB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAGNvD,OAAA;MAAKkD,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CnD,OAAA;QAAKkD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BnD,OAAA,CAACF,IAAI;UAACoD,SAAS,EAAC;QAAiD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEvD,OAAA;UAAKkD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCnD,OAAA;YAAGkD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtDvD,OAAA;YAAIkD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC/BnD,OAAA;cAAAmD,QAAA,GAAI,8BAAuB,EAACvC,OAAO,CAACiC,YAAY,EAAC,SAAO,GAAApC,sBAAA,GAACG,OAAO,CAACyB,cAAc,cAAA5B,sBAAA,uBAAtBA,sBAAA,CAAwBsD,cAAc,CAAC,CAAC,EAAC,QAAM;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7GvD,OAAA;cAAAmD,QAAA,GAAI,+BAAwB,EAACvC,OAAO,CAAC4B,QAAQ,EAAC,QAAM;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDvD,OAAA;cAAAmD,QAAA,GAAI,0BAAmB,EAACvC,OAAO,CAAC6B,SAAS,EAAC,QAAM;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACpDnD,YAAY,GAAG,CAAC,iBACfJ,OAAA;cAAAmD,QAAA,GAAI,+CAAwC,EAACzB,IAAI,CAAC6D,KAAK,CAACnF,YAAY,IAAIQ,OAAO,CAAC4B,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAC,yBAAuB;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC5H;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnD,YAAY,GAAGQ,OAAO,CAAC4B,QAAQ,GAAG,CAAC,iBAClCxC,OAAA;MAAKkD,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtEnD,OAAA;QAAKkD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BnD,OAAA,CAACH,aAAa;UAACqD,SAAS,EAAC;QAAkD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9EvD,OAAA;UAAKkD,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtBnD,OAAA;YAAGkD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/DvD,OAAA;YAAGkD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAGnC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChD,EAAA,CAzOIN,mBAAmB;AAAAuF,EAAA,GAAnBvF,mBAAmB;AA2OzB,eAAeA,mBAAmB;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}