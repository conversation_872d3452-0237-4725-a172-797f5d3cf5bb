[{"F:\\SIC BigData\\InventoryManagement\\src\\index.js": "1", "F:\\SIC BigData\\InventoryManagement\\src\\App.js": "2", "F:\\SIC BigData\\InventoryManagement\\src\\components\\ProtectedRoute.js": "3", "F:\\SIC BigData\\InventoryManagement\\src\\contexts\\AuthContext.js": "4", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Dashboard.js": "5", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Layout.js": "6", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Inventory.js": "7", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Login.js": "8", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\SalesOrders.js": "9", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Reports.js": "10", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Suppliers.js": "11", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Sidebar.js": "12", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Header.js": "13", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\InventoryChart.js": "14", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\StatsCard.js": "15", "F:\\SIC BigData\\InventoryManagement\\src\\services\\api.js": "16", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\RecentOrders.js": "17", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\LowStockAlert.js": "18", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\AddInventoryModal.js": "19", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\InventoryTable.js": "20", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Settings.js": "21", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Reports\\ExportModal.js": "22", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Suppliers\\AddSupplierModal.js": "23", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Orders\\AddOrderModal.js": "24", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\InstacartAnalysis.js": "25", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Predictive\\ReorderSuggestions.js": "26", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Predictive\\DemandForecastChart.js": "27", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Predictive\\PredictiveSettings.js": "28"}, {"size": 599, "mtime": 1753275041292, "results": "29", "hashOfConfig": "30"}, {"size": 2581, "mtime": 1754395980430, "results": "31", "hashOfConfig": "30"}, {"size": 1965, "mtime": 1753281864341, "results": "32", "hashOfConfig": "30"}, {"size": 7097, "mtime": 1753281822017, "results": "33", "hashOfConfig": "30"}, {"size": 7100, "mtime": 1754402039105, "results": "34", "hashOfConfig": "30"}, {"size": 743, "mtime": 1753275063328, "results": "35", "hashOfConfig": "30"}, {"size": 18446, "mtime": 1754402228661, "results": "36", "hashOfConfig": "30"}, {"size": 7204, "mtime": 1753281850508, "results": "37", "hashOfConfig": "30"}, {"size": 14941, "mtime": 1754393307713, "results": "38", "hashOfConfig": "30"}, {"size": 19636, "mtime": 1754398715121, "results": "39", "hashOfConfig": "30"}, {"size": 16262, "mtime": 1754394995292, "results": "40", "hashOfConfig": "30"}, {"size": 3505, "mtime": 1754396020695, "results": "41", "hashOfConfig": "30"}, {"size": 4462, "mtime": 1753281912430, "results": "42", "hashOfConfig": "30"}, {"size": 1226, "mtime": 1753275145224, "results": "43", "hashOfConfig": "30"}, {"size": 1194, "mtime": 1753275129694, "results": "44", "hashOfConfig": "30"}, {"size": 7945, "mtime": 1754402639597, "results": "45", "hashOfConfig": "30"}, {"size": 4144, "mtime": 1753275178958, "results": "46", "hashOfConfig": "30"}, {"size": 2047, "mtime": 1753275159082, "results": "47", "hashOfConfig": "30"}, {"size": 19536, "mtime": 1754392290135, "results": "48", "hashOfConfig": "30"}, {"size": 7308, "mtime": 1754402216158, "results": "49", "hashOfConfig": "30"}, {"size": 23347, "mtime": 1754402498252, "results": "50", "hashOfConfig": "30"}, {"size": 14000, "mtime": 1754392543260, "results": "51", "hashOfConfig": "30"}, {"size": 20157, "mtime": 1754392373357, "results": "52", "hashOfConfig": "30"}, {"size": 24597, "mtime": 1754392484422, "results": "53", "hashOfConfig": "30"}, {"size": 9911, "mtime": 1754396463283, "results": "54", "hashOfConfig": "30"}, {"size": 11201, "mtime": 1754401858101, "results": "55", "hashOfConfig": "30"}, {"size": 8718, "mtime": 1754401814004, "results": "56", "hashOfConfig": "30"}, {"size": 18683, "mtime": 1754402731333, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ar5hf7", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\SIC BigData\\InventoryManagement\\src\\index.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\App.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\ProtectedRoute.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\contexts\\AuthContext.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Dashboard.js", ["142", "143"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Layout.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Inventory.js", ["144"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Login.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\SalesOrders.js", ["145"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Reports.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Suppliers.js", ["146"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Sidebar.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Header.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\InventoryChart.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\StatsCard.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\services\\api.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\RecentOrders.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\LowStockAlert.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\AddInventoryModal.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\InventoryTable.js", ["147", "148", "149"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Settings.js", ["150", "151", "152", "153", "154", "155"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Reports\\ExportModal.js", ["156", "157", "158"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Suppliers\\AddSupplierModal.js", ["159"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Orders\\AddOrderModal.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\InstacartAnalysis.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Predictive\\ReorderSuggestions.js", ["160", "161"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Predictive\\DemandForecastChart.js", ["162", "163"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Predictive\\PredictiveSettings.js", ["164", "165", "166", "167"], [], {"ruleId": "168", "severity": 1, "message": "169", "line": 6, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 6, "endColumn": 13}, {"ruleId": "168", "severity": 1, "message": "172", "line": 7, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 7, "endColumn": 16}, {"ruleId": "168", "severity": 1, "message": "173", "line": 34, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 34, "endColumn": 25}, {"ruleId": "168", "severity": 1, "message": "174", "line": 7, "column": 7, "nodeType": "170", "messageId": "171", "endLine": 7, "endColumn": 22}, {"ruleId": "168", "severity": 1, "message": "175", "line": 7, "column": 7, "nodeType": "170", "messageId": "171", "endLine": 7, "endColumn": 20}, {"ruleId": "168", "severity": 1, "message": "176", "line": 2, "column": 24, "nodeType": "170", "messageId": "171", "endLine": 2, "endColumn": 27}, {"ruleId": "168", "severity": 1, "message": "177", "line": 9, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 9, "endColumn": 23}, {"ruleId": "168", "severity": 1, "message": "178", "line": 9, "column": 25, "nodeType": "170", "messageId": "171", "endLine": 9, "endColumn": 41}, {"ruleId": "168", "severity": 1, "message": "179", "line": 7, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 7, "endColumn": 7}, {"ruleId": "168", "severity": 1, "message": "180", "line": 8, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 8, "endColumn": 8}, {"ruleId": "168", "severity": 1, "message": "181", "line": 12, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 12, "endColumn": 8}, {"ruleId": "168", "severity": 1, "message": "182", "line": 13, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 13, "endColumn": 4}, {"ruleId": "168", "severity": 1, "message": "183", "line": 16, "column": 19, "nodeType": "170", "messageId": "171", "endLine": 16, "endColumn": 27}, {"ruleId": "184", "severity": 1, "message": "185", "line": 65, "column": 6, "nodeType": "186", "endLine": 65, "endColumn": 8, "suggestions": "187"}, {"ruleId": "168", "severity": 1, "message": "188", "line": 2, "column": 60, "nodeType": "170", "messageId": "171", "endLine": 2, "endColumn": 66}, {"ruleId": "168", "severity": 1, "message": "189", "line": 77, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 77, "endColumn": 26}, {"ruleId": "168", "severity": 1, "message": "190", "line": 143, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 143, "endColumn": 24}, {"ruleId": "168", "severity": 1, "message": "191", "line": 55, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 55, "endColumn": 24}, {"ruleId": "168", "severity": 1, "message": "192", "line": 20, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 20, "endColumn": 28}, {"ruleId": "184", "severity": 1, "message": "193", "line": 24, "column": 6, "nodeType": "186", "endLine": 24, "endColumn": 21, "suggestions": "194"}, {"ruleId": "168", "severity": 1, "message": "195", "line": 3, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 3, "endColumn": 12}, {"ruleId": "184", "severity": 1, "message": "196", "line": 31, "column": 6, "nodeType": "186", "endLine": 31, "endColumn": 34, "suggestions": "197"}, {"ruleId": "168", "severity": 1, "message": "198", "line": 4, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 4, "endColumn": 8}, {"ruleId": "168", "severity": 1, "message": "172", "line": 6, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 6, "endColumn": 16}, {"ruleId": "168", "severity": 1, "message": "199", "line": 10, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 10, "endColumn": 8}, {"ruleId": "168", "severity": 1, "message": "200", "line": 113, "column": 11, "nodeType": "170", "messageId": "171", "endLine": 113, "endColumn": 17}, "no-unused-vars", "'TrendingUp' is defined but never used.", "Identifier", "unusedVar", "'AlertTriangle' is defined but never used.", "'forecastLoading' is assigned a value but never used.", "'salesOrdersData' is assigned a value but never used.", "'suppliersData' is assigned a value but never used.", "'Eye' is defined but never used.", "'selectedItems' is assigned a value but never used.", "'setSelectedItems' is assigned a value but never used.", "'Mail' is defined but never used.", "'Globe' is defined but never used.", "'Check' is defined but never used.", "'X' is defined but never used.", "'usersAPI' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["201"], "'Filter' is defined but never used.", "'handleFieldToggle' is assigned a value but never used.", "'getFieldOptions' is assigned a value but never used.", "'currencyOptions' is assigned a value but never used.", "'selectedSuggestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSuggestions'. Either include it or remove the dependency array.", ["202"], "'LineChart' is defined but never used.", "React Hook useEffect has a missing dependency: 'processChartData'. Either include it or remove the dependency array.", ["203"], "'Brain' is defined but never used.", "'Pause' is defined but never used.", "'result' is assigned a value but never used.", {"desc": "204", "fix": "205"}, {"desc": "206", "fix": "207"}, {"desc": "208", "fix": "209"}, "Update the dependencies array to be: [fetchUserProfile]", {"range": "210", "text": "211"}, "Update the dependencies array to be: [fetchSuggestions, filter, limit]", {"range": "212", "text": "213"}, "Update the dependencies array to be: [forecastData, currentStock, processChartData]", {"range": "214", "text": "215"}, [1411, 1413], "[fetchUserProfile]", [629, 644], "[fetchSuggestions, filter, limit]", [633, 661], "[forecastData, currentStock, processChartData]"]