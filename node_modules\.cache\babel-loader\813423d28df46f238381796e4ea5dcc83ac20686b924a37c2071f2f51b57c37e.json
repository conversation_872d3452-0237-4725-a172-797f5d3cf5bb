{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\components\\\\Predictive\\\\ReorderSuggestions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { AlertTriangle, Clock, Package, TrendingUp, CheckCircle, XCircle, Eye, ShoppingCart, Calendar } from 'lucide-react';\nimport { predictiveAPI } from '../../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReorderSuggestions = ({\n  limit = 10,\n  showActions = true\n}) => {\n  _s();\n  const [suggestions, setSuggestions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('all');\n  const [selectedSuggestion, setSelectedSuggestion] = useState(null);\n  useEffect(() => {\n    fetchSuggestions();\n  }, [filter, limit]);\n  const fetchSuggestions = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        limit\n      };\n      if (filter !== 'all') {\n        params.urgency = filter;\n      }\n      const response = await predictiveAPI.getReorderSuggestions(params);\n      setSuggestions(response.data.suggestions || []);\n    } catch (error) {\n      console.error('Error fetching reorder suggestions:', error);\n      toast.error('Failed to load reorder suggestions');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleUpdateSuggestion = async (id, status, notes = '') => {\n    try {\n      await predictiveAPI.updateReorderSuggestion(id, {\n        status,\n        review_notes: notes\n      });\n      toast.success(`Suggestion ${status} successfully`);\n      fetchSuggestions(); // Refresh the list\n    } catch (error) {\n      console.error('Error updating suggestion:', error);\n      toast.error('Failed to update suggestion');\n    }\n  };\n  const getUrgencyColor = urgency => {\n    switch (urgency) {\n      case 'Critical':\n        return 'text-red-600 bg-red-100';\n      case 'High':\n        return 'text-orange-600 bg-orange-100';\n      case 'Normal':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'Low':\n        return 'text-green-600 bg-green-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getUrgencyIcon = urgency => {\n    switch (urgency) {\n      case 'Critical':\n        return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 31\n        }, this);\n      case 'High':\n        return /*#__PURE__*/_jsxDEV(Clock, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 27\n        }, this);\n      case 'Normal':\n        return /*#__PURE__*/_jsxDEV(Package, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 29\n        }, this);\n      case 'Low':\n        return /*#__PURE__*/_jsxDEV(TrendingUp, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 26\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Package, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const getRiskColor = risk => {\n    if (risk >= 80) return 'text-red-600';\n    if (risk >= 60) return 'text-orange-600';\n    if (risk >= 40) return 'text-yellow-600';\n    return 'text-green-600';\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [...Array(3)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-16 bg-gray-200 rounded\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {\n            className: \"h-5 w-5 mr-2 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), \"Reorder Suggestions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: ['all', 'Critical', 'High', 'Normal', 'Low'].map(filterOption => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setFilter(filterOption),\n            className: `px-3 py-1 text-xs font-medium rounded-full transition-colors ${filter === filterOption ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n            children: filterOption === 'all' ? 'All' : filterOption\n          }, filterOption, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divide-y divide-gray-200\",\n      children: suggestions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 text-center text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(Package, {\n          className: \"h-12 w-12 mx-auto mb-3 text-gray-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No reorder suggestions found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm mt-1\",\n          children: \"All products are well-stocked!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this) : suggestions.map(suggestion => {\n        var _suggestion$current_s, _suggestion$suggested, _suggestion$predicted, _suggestion$predicted2, _suggestion$predicted3;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 hover:bg-gray-50 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: suggestion.product_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [\"SKU: \", suggestion.sku]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(suggestion.urgency_level)}`,\n                  children: [getUrgencyIcon(suggestion.urgency_level), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: suggestion.urgency_level\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Current Stock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: [(_suggestion$current_s = suggestion.current_stock) === null || _suggestion$current_s === void 0 ? void 0 : _suggestion$current_s.toLocaleString(), \" units\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Suggested Quantity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-blue-600\",\n                    children: [(_suggestion$suggested = suggestion.suggested_reorder_quantity) === null || _suggestion$suggested === void 0 ? void 0 : _suggestion$suggested.toLocaleString(), \" units\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Stockout Risk\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `font-semibold ${getRiskColor(suggestion.stockout_risk_percentage)}`,\n                    children: [suggestion.stockout_risk_percentage, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Days Until Stockout\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: [suggestion.days_until_stockout || 'N/A', \" days\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-3 gap-4 mb-3 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 rounded p-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-blue-600 font-medium\",\n                    children: \"7-Day Demand\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-blue-900 font-semibold\",\n                    children: [(_suggestion$predicted = suggestion.predicted_demand_7_days) === null || _suggestion$predicted === void 0 ? void 0 : _suggestion$predicted.toLocaleString(), \" units\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-50 rounded p-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-purple-600 font-medium\",\n                    children: \"14-Day Demand\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-purple-900 font-semibold\",\n                    children: [(_suggestion$predicted2 = suggestion.predicted_demand_14_days) === null || _suggestion$predicted2 === void 0 ? void 0 : _suggestion$predicted2.toLocaleString(), \" units\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 rounded p-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-green-600 font-medium\",\n                    children: \"30-Day Demand\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-green-900 font-semibold\",\n                    children: [(_suggestion$predicted3 = suggestion.predicted_demand_30_days) === null || _suggestion$predicted3 === void 0 ? void 0 : _suggestion$predicted3.toLocaleString(), \" units\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this), \"Suggested Date: \", formatDate(suggestion.suggested_reorder_date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 23\n                  }, this), \"Trend: \", suggestion.velocity_trend]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 text-sm text-gray-600 bg-gray-50 rounded p-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Reason:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this), \" \", suggestion.recommendation_reason]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), showActions && suggestion.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col space-y-2 ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleUpdateSuggestion(suggestion._id, 'approved'),\n                className: \"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 23\n                }, this), \"Approve\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleUpdateSuggestion(suggestion._id, 'dismissed'),\n                className: \"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), \"Dismiss\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedSuggestion(suggestion),\n                className: \"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this), \"Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)\n        }, suggestion._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), suggestions.length >= limit && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t border-gray-200 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => fetchSuggestions(),\n        className: \"text-blue-600 hover:text-blue-700 text-sm font-medium\",\n        children: \"View All Suggestions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_s(ReorderSuggestions, \"IcB1S81Kw5XSwDsolWCTBY0zNZA=\");\n_c = ReorderSuggestions;\nexport default ReorderSuggestions;\nvar _c;\n$RefreshReg$(_c, \"ReorderSuggestions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Package", "TrendingUp", "CheckCircle", "XCircle", "Eye", "ShoppingCart", "Calendar", "predictiveAPI", "toast", "jsxDEV", "_jsxDEV", "ReorderSuggestions", "limit", "showActions", "_s", "suggestions", "setSuggestions", "loading", "setLoading", "filter", "setFilter", "selectedSuggestion", "setSelectedSuggestion", "fetchSuggestions", "params", "urgency", "response", "getReorderSuggestions", "data", "error", "console", "handleUpdateSuggestion", "id", "status", "notes", "updateReorderSuggestion", "review_notes", "success", "getUrgencyColor", "getUrgencyIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getRiskColor", "risk", "formatDate", "dateString", "Date", "toLocaleDateString", "month", "day", "year", "children", "Array", "map", "_", "i", "filterOption", "onClick", "length", "suggestion", "_suggestion$current_s", "_suggestion$suggested", "_suggestion$predicted", "_suggestion$predicted2", "_suggestion$predicted3", "product_name", "sku", "urgency_level", "current_stock", "toLocaleString", "suggested_reorder_quantity", "stockout_risk_percentage", "days_until_stockout", "predicted_demand_7_days", "predicted_demand_14_days", "predicted_demand_30_days", "suggested_reorder_date", "velocity_trend", "recommendation_reason", "_id", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/components/Predictive/ReorderSuggestions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  AlertTriangle, \n  Clock, \n  Package, \n  TrendingUp, \n  CheckCircle, \n  XCircle,\n  Eye,\n  ShoppingCart,\n  Calendar\n} from 'lucide-react';\nimport { predictiveAPI } from '../../services/api';\nimport toast from 'react-hot-toast';\n\nconst ReorderSuggestions = ({ limit = 10, showActions = true }) => {\n  const [suggestions, setSuggestions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('all');\n  const [selectedSuggestion, setSelectedSuggestion] = useState(null);\n\n  useEffect(() => {\n    fetchSuggestions();\n  }, [filter, limit]);\n\n  const fetchSuggestions = async () => {\n    try {\n      setLoading(true);\n      const params = { limit };\n      if (filter !== 'all') {\n        params.urgency = filter;\n      }\n      \n      const response = await predictiveAPI.getReorderSuggestions(params);\n      setSuggestions(response.data.suggestions || []);\n    } catch (error) {\n      console.error('Error fetching reorder suggestions:', error);\n      toast.error('Failed to load reorder suggestions');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUpdateSuggestion = async (id, status, notes = '') => {\n    try {\n      await predictiveAPI.updateReorderSuggestion(id, { \n        status, \n        review_notes: notes \n      });\n      \n      toast.success(`Suggestion ${status} successfully`);\n      fetchSuggestions(); // Refresh the list\n    } catch (error) {\n      console.error('Error updating suggestion:', error);\n      toast.error('Failed to update suggestion');\n    }\n  };\n\n  const getUrgencyColor = (urgency) => {\n    switch (urgency) {\n      case 'Critical': return 'text-red-600 bg-red-100';\n      case 'High': return 'text-orange-600 bg-orange-100';\n      case 'Normal': return 'text-yellow-600 bg-yellow-100';\n      case 'Low': return 'text-green-600 bg-green-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getUrgencyIcon = (urgency) => {\n    switch (urgency) {\n      case 'Critical': return <AlertTriangle className=\"h-4 w-4\" />;\n      case 'High': return <Clock className=\"h-4 w-4\" />;\n      case 'Normal': return <Package className=\"h-4 w-4\" />;\n      case 'Low': return <TrendingUp className=\"h-4 w-4\" />;\n      default: return <Package className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getRiskColor = (risk) => {\n    if (risk >= 80) return 'text-red-600';\n    if (risk >= 60) return 'text-orange-600';\n    if (risk >= 40) return 'text-yellow-600';\n    return 'text-green-600';\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-6 bg-gray-200 rounded w-1/3 mb-4\"></div>\n          <div className=\"space-y-3\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"h-16 bg-gray-200 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n            <ShoppingCart className=\"h-5 w-5 mr-2 text-blue-600\" />\n            Reorder Suggestions\n          </h3>\n          \n          {/* Filter Buttons */}\n          <div className=\"flex space-x-2\">\n            {['all', 'Critical', 'High', 'Normal', 'Low'].map((filterOption) => (\n              <button\n                key={filterOption}\n                onClick={() => setFilter(filterOption)}\n                className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${\n                  filter === filterOption\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                }`}\n              >\n                {filterOption === 'all' ? 'All' : filterOption}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Suggestions List */}\n      <div className=\"divide-y divide-gray-200\">\n        {suggestions.length === 0 ? (\n          <div className=\"p-6 text-center text-gray-500\">\n            <Package className=\"h-12 w-12 mx-auto mb-3 text-gray-300\" />\n            <p>No reorder suggestions found</p>\n            <p className=\"text-sm mt-1\">All products are well-stocked!</p>\n          </div>\n        ) : (\n          suggestions.map((suggestion) => (\n            <div key={suggestion._id} className=\"p-6 hover:bg-gray-50 transition-colors\">\n              <div className=\"flex items-start justify-between\">\n                {/* Product Info */}\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <h4 className=\"font-medium text-gray-900\">\n                      {suggestion.product_name}\n                    </h4>\n                    <span className=\"text-sm text-gray-500\">\n                      SKU: {suggestion.sku}\n                    </span>\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(suggestion.urgency_level)}`}>\n                      {getUrgencyIcon(suggestion.urgency_level)}\n                      <span className=\"ml-1\">{suggestion.urgency_level}</span>\n                    </span>\n                  </div>\n\n                  {/* Metrics Grid */}\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-3\">\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Current Stock</div>\n                      <div className=\"font-semibold text-gray-900\">\n                        {suggestion.current_stock?.toLocaleString()} units\n                      </div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Suggested Quantity</div>\n                      <div className=\"font-semibold text-blue-600\">\n                        {suggestion.suggested_reorder_quantity?.toLocaleString()} units\n                      </div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Stockout Risk</div>\n                      <div className={`font-semibold ${getRiskColor(suggestion.stockout_risk_percentage)}`}>\n                        {suggestion.stockout_risk_percentage}%\n                      </div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Days Until Stockout</div>\n                      <div className=\"font-semibold text-gray-900\">\n                        {suggestion.days_until_stockout || 'N/A'} days\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Demand Predictions */}\n                  <div className=\"grid grid-cols-3 gap-4 mb-3 text-sm\">\n                    <div className=\"bg-blue-50 rounded p-2\">\n                      <div className=\"text-xs text-blue-600 font-medium\">7-Day Demand</div>\n                      <div className=\"text-blue-900 font-semibold\">\n                        {suggestion.predicted_demand_7_days?.toLocaleString()} units\n                      </div>\n                    </div>\n                    <div className=\"bg-purple-50 rounded p-2\">\n                      <div className=\"text-xs text-purple-600 font-medium\">14-Day Demand</div>\n                      <div className=\"text-purple-900 font-semibold\">\n                        {suggestion.predicted_demand_14_days?.toLocaleString()} units\n                      </div>\n                    </div>\n                    <div className=\"bg-green-50 rounded p-2\">\n                      <div className=\"text-xs text-green-600 font-medium\">30-Day Demand</div>\n                      <div className=\"text-green-900 font-semibold\">\n                        {suggestion.predicted_demand_30_days?.toLocaleString()} units\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Additional Info */}\n                  <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                    <div className=\"flex items-center\">\n                      <Calendar className=\"h-4 w-4 mr-1\" />\n                      Suggested Date: {formatDate(suggestion.suggested_reorder_date)}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <TrendingUp className=\"h-4 w-4 mr-1\" />\n                      Trend: {suggestion.velocity_trend}\n                    </div>\n                  </div>\n\n                  {/* Recommendation Reason */}\n                  <div className=\"mt-2 text-sm text-gray-600 bg-gray-50 rounded p-2\">\n                    <strong>Reason:</strong> {suggestion.recommendation_reason}\n                  </div>\n                </div>\n\n                {/* Actions */}\n                {showActions && suggestion.status === 'pending' && (\n                  <div className=\"flex flex-col space-y-2 ml-4\">\n                    <button\n                      onClick={() => handleUpdateSuggestion(suggestion._id, 'approved')}\n                      className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 transition-colors\"\n                    >\n                      <CheckCircle className=\"h-3 w-3 mr-1\" />\n                      Approve\n                    </button>\n                    <button\n                      onClick={() => handleUpdateSuggestion(suggestion._id, 'dismissed')}\n                      className=\"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors\"\n                    >\n                      <XCircle className=\"h-3 w-3 mr-1\" />\n                      Dismiss\n                    </button>\n                    <button\n                      onClick={() => setSelectedSuggestion(suggestion)}\n                      className=\"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors\"\n                    >\n                      <Eye className=\"h-3 w-3 mr-1\" />\n                      Details\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* Show More Button */}\n      {suggestions.length >= limit && (\n        <div className=\"p-4 border-t border-gray-200 text-center\">\n          <button\n            onClick={() => fetchSuggestions()}\n            className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n          >\n            View All Suggestions\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ReorderSuggestions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,aAAa,EACbC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,WAAW,EACXC,OAAO,EACPC,GAAG,EACHC,YAAY,EACZC,QAAQ,QACH,cAAc;AACrB,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,KAAK,GAAG,EAAE;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAElEC,SAAS,CAAC,MAAM;IACd0B,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACJ,MAAM,EAAEP,KAAK,CAAC,CAAC;EAEnB,MAAMW,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,MAAM,GAAG;QAAEZ;MAAM,CAAC;MACxB,IAAIO,MAAM,KAAK,KAAK,EAAE;QACpBK,MAAM,CAACC,OAAO,GAAGN,MAAM;MACzB;MAEA,MAAMO,QAAQ,GAAG,MAAMnB,aAAa,CAACoB,qBAAqB,CAACH,MAAM,CAAC;MAClER,cAAc,CAACU,QAAQ,CAACE,IAAI,CAACb,WAAW,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DrB,KAAK,CAACqB,KAAK,CAAC,oCAAoC,CAAC;IACnD,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,sBAAsB,GAAG,MAAAA,CAAOC,EAAE,EAAEC,MAAM,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC/D,IAAI;MACF,MAAM3B,aAAa,CAAC4B,uBAAuB,CAACH,EAAE,EAAE;QAC9CC,MAAM;QACNG,YAAY,EAAEF;MAChB,CAAC,CAAC;MAEF1B,KAAK,CAAC6B,OAAO,CAAC,cAAcJ,MAAM,eAAe,CAAC;MAClDV,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDrB,KAAK,CAACqB,KAAK,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC;EAED,MAAMS,eAAe,GAAIb,OAAO,IAAK;IACnC,QAAQA,OAAO;MACb,KAAK,UAAU;QAAE,OAAO,yBAAyB;MACjD,KAAK,MAAM;QAAE,OAAO,+BAA+B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,KAAK;QAAE,OAAO,6BAA6B;MAChD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMc,cAAc,GAAId,OAAO,IAAK;IAClC,QAAQA,OAAO;MACb,KAAK,UAAU;QAAE,oBAAOf,OAAA,CAACZ,aAAa;UAAC0C,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,MAAM;QAAE,oBAAOlC,OAAA,CAACX,KAAK;UAACyC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjD,KAAK,QAAQ;QAAE,oBAAOlC,OAAA,CAACV,OAAO;UAACwC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,KAAK;QAAE,oBAAOlC,OAAA,CAACT,UAAU;UAACuC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD;QAAS,oBAAOlC,OAAA,CAACV,OAAO;UAACwC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACjD;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,IAAI,IAAK;IAC7B,IAAIA,IAAI,IAAI,EAAE,EAAE,OAAO,cAAc;IACrC,IAAIA,IAAI,IAAI,EAAE,EAAE,OAAO,iBAAiB;IACxC,IAAIA,IAAI,IAAI,EAAE,EAAE,OAAO,iBAAiB;IACxC,OAAO,gBAAgB;EACzB,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,IAAIpC,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK8B,SAAS,EAAC,0DAA0D;MAAAc,QAAA,eACvE5C,OAAA;QAAK8B,SAAS,EAAC,eAAe;QAAAc,QAAA,gBAC5B5C,OAAA;UAAK8B,SAAS,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DlC,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAc,QAAA,EACvB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBhD,OAAA;YAAa8B,SAAS,EAAC;UAA0B,GAAvCkB,CAAC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA4C,CACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK8B,SAAS,EAAC,sDAAsD;IAAAc,QAAA,gBAEnE5C,OAAA;MAAK8B,SAAS,EAAC,8BAA8B;MAAAc,QAAA,eAC3C5C,OAAA;QAAK8B,SAAS,EAAC,mCAAmC;QAAAc,QAAA,gBAChD5C,OAAA;UAAI8B,SAAS,EAAC,uDAAuD;UAAAc,QAAA,gBACnE5C,OAAA,CAACL,YAAY;YAACmC,SAAS,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAEzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGLlC,OAAA;UAAK8B,SAAS,EAAC,gBAAgB;UAAAc,QAAA,EAC5B,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAACE,GAAG,CAAEG,YAAY,iBAC7DjD,OAAA;YAEEkD,OAAO,EAAEA,CAAA,KAAMxC,SAAS,CAACuC,YAAY,CAAE;YACvCnB,SAAS,EAAE,gEACTrB,MAAM,KAAKwC,YAAY,GACnB,2BAA2B,GAC3B,6CAA6C,EAChD;YAAAL,QAAA,EAEFK,YAAY,KAAK,KAAK,GAAG,KAAK,GAAGA;UAAY,GARzCA,YAAY;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASX,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAK8B,SAAS,EAAC,0BAA0B;MAAAc,QAAA,EACtCvC,WAAW,CAAC8C,MAAM,KAAK,CAAC,gBACvBnD,OAAA;QAAK8B,SAAS,EAAC,+BAA+B;QAAAc,QAAA,gBAC5C5C,OAAA,CAACV,OAAO;UAACwC,SAAS,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DlC,OAAA;UAAA4C,QAAA,EAAG;QAA4B;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnClC,OAAA;UAAG8B,SAAS,EAAC,cAAc;UAAAc,QAAA,EAAC;QAA8B;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,GAEN7B,WAAW,CAACyC,GAAG,CAAEM,UAAU;QAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAAA,oBACzBzD,OAAA;UAA0B8B,SAAS,EAAC,wCAAwC;UAAAc,QAAA,eAC1E5C,OAAA;YAAK8B,SAAS,EAAC,kCAAkC;YAAAc,QAAA,gBAE/C5C,OAAA;cAAK8B,SAAS,EAAC,QAAQ;cAAAc,QAAA,gBACrB5C,OAAA;gBAAK8B,SAAS,EAAC,kCAAkC;gBAAAc,QAAA,gBAC/C5C,OAAA;kBAAI8B,SAAS,EAAC,2BAA2B;kBAAAc,QAAA,EACtCQ,UAAU,CAACM;gBAAY;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACLlC,OAAA;kBAAM8B,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,GAAC,OACjC,EAACQ,UAAU,CAACO,GAAG;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACPlC,OAAA;kBAAM8B,SAAS,EAAE,uEAAuEF,eAAe,CAACwB,UAAU,CAACQ,aAAa,CAAC,EAAG;kBAAAhB,QAAA,GACjIf,cAAc,CAACuB,UAAU,CAACQ,aAAa,CAAC,eACzC5D,OAAA;oBAAM8B,SAAS,EAAC,MAAM;oBAAAc,QAAA,EAAEQ,UAAU,CAACQ;kBAAa;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGNlC,OAAA;gBAAK8B,SAAS,EAAC,4CAA4C;gBAAAc,QAAA,gBACzD5C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAK8B,SAAS,EAAC,uBAAuB;oBAAAc,QAAA,EAAC;kBAAa;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1DlC,OAAA;oBAAK8B,SAAS,EAAC,6BAA6B;oBAAAc,QAAA,IAAAS,qBAAA,GACzCD,UAAU,CAACS,aAAa,cAAAR,qBAAA,uBAAxBA,qBAAA,CAA0BS,cAAc,CAAC,CAAC,EAAC,QAC9C;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlC,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAK8B,SAAS,EAAC,uBAAuB;oBAAAc,QAAA,EAAC;kBAAkB;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/DlC,OAAA;oBAAK8B,SAAS,EAAC,6BAA6B;oBAAAc,QAAA,IAAAU,qBAAA,GACzCF,UAAU,CAACW,0BAA0B,cAAAT,qBAAA,uBAArCA,qBAAA,CAAuCQ,cAAc,CAAC,CAAC,EAAC,QAC3D;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlC,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAK8B,SAAS,EAAC,uBAAuB;oBAAAc,QAAA,EAAC;kBAAa;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1DlC,OAAA;oBAAK8B,SAAS,EAAE,iBAAiBK,YAAY,CAACiB,UAAU,CAACY,wBAAwB,CAAC,EAAG;oBAAApB,QAAA,GAClFQ,UAAU,CAACY,wBAAwB,EAAC,GACvC;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlC,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAK8B,SAAS,EAAC,uBAAuB;oBAAAc,QAAA,EAAC;kBAAmB;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChElC,OAAA;oBAAK8B,SAAS,EAAC,6BAA6B;oBAAAc,QAAA,GACzCQ,UAAU,CAACa,mBAAmB,IAAI,KAAK,EAAC,OAC3C;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlC,OAAA;gBAAK8B,SAAS,EAAC,qCAAqC;gBAAAc,QAAA,gBAClD5C,OAAA;kBAAK8B,SAAS,EAAC,wBAAwB;kBAAAc,QAAA,gBACrC5C,OAAA;oBAAK8B,SAAS,EAAC,mCAAmC;oBAAAc,QAAA,EAAC;kBAAY;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrElC,OAAA;oBAAK8B,SAAS,EAAC,6BAA6B;oBAAAc,QAAA,IAAAW,qBAAA,GACzCH,UAAU,CAACc,uBAAuB,cAAAX,qBAAA,uBAAlCA,qBAAA,CAAoCO,cAAc,CAAC,CAAC,EAAC,QACxD;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlC,OAAA;kBAAK8B,SAAS,EAAC,0BAA0B;kBAAAc,QAAA,gBACvC5C,OAAA;oBAAK8B,SAAS,EAAC,qCAAqC;oBAAAc,QAAA,EAAC;kBAAa;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxElC,OAAA;oBAAK8B,SAAS,EAAC,+BAA+B;oBAAAc,QAAA,IAAAY,sBAAA,GAC3CJ,UAAU,CAACe,wBAAwB,cAAAX,sBAAA,uBAAnCA,sBAAA,CAAqCM,cAAc,CAAC,CAAC,EAAC,QACzD;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlC,OAAA;kBAAK8B,SAAS,EAAC,yBAAyB;kBAAAc,QAAA,gBACtC5C,OAAA;oBAAK8B,SAAS,EAAC,oCAAoC;oBAAAc,QAAA,EAAC;kBAAa;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvElC,OAAA;oBAAK8B,SAAS,EAAC,8BAA8B;oBAAAc,QAAA,IAAAa,sBAAA,GAC1CL,UAAU,CAACgB,wBAAwB,cAAAX,sBAAA,uBAAnCA,sBAAA,CAAqCK,cAAc,CAAC,CAAC,EAAC,QACzD;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlC,OAAA;gBAAK8B,SAAS,EAAC,mDAAmD;gBAAAc,QAAA,gBAChE5C,OAAA;kBAAK8B,SAAS,EAAC,mBAAmB;kBAAAc,QAAA,gBAChC5C,OAAA,CAACJ,QAAQ;oBAACkC,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBACrB,EAACG,UAAU,CAACe,UAAU,CAACiB,sBAAsB,CAAC;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACNlC,OAAA;kBAAK8B,SAAS,EAAC,mBAAmB;kBAAAc,QAAA,gBAChC5C,OAAA,CAACT,UAAU;oBAACuC,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAChC,EAACkB,UAAU,CAACkB,cAAc;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlC,OAAA;gBAAK8B,SAAS,EAAC,mDAAmD;gBAAAc,QAAA,gBAChE5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkB,UAAU,CAACmB,qBAAqB;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL/B,WAAW,IAAIiD,UAAU,CAAC7B,MAAM,KAAK,SAAS,iBAC7CvB,OAAA;cAAK8B,SAAS,EAAC,8BAA8B;cAAAc,QAAA,gBAC3C5C,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAM7B,sBAAsB,CAAC+B,UAAU,CAACoB,GAAG,EAAE,UAAU,CAAE;gBAClE1C,SAAS,EAAC,uJAAuJ;gBAAAc,QAAA,gBAEjK5C,OAAA,CAACR,WAAW;kBAACsC,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlC,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAM7B,sBAAsB,CAAC+B,UAAU,CAACoB,GAAG,EAAE,WAAW,CAAE;gBACnE1C,SAAS,EAAC,iJAAiJ;gBAAAc,QAAA,gBAE3J5C,OAAA,CAACP,OAAO;kBAACqC,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlC,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAMtC,qBAAqB,CAACwC,UAAU,CAAE;gBACjDtB,SAAS,EAAC,iJAAiJ;gBAAAc,QAAA,gBAE3J5C,OAAA,CAACN,GAAG;kBAACoC,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAElC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA/GEkB,UAAU,CAACoB,GAAG;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgHnB,CAAC;MAAA,CACP;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL7B,WAAW,CAAC8C,MAAM,IAAIjD,KAAK,iBAC1BF,OAAA;MAAK8B,SAAS,EAAC,0CAA0C;MAAAc,QAAA,eACvD5C,OAAA;QACEkD,OAAO,EAAEA,CAAA,KAAMrC,gBAAgB,CAAC,CAAE;QAClCiB,SAAS,EAAC,uDAAuD;QAAAc,QAAA,EAClE;MAED;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAtQIH,kBAAkB;AAAAwE,EAAA,GAAlBxE,kBAAkB;AAwQxB,eAAeA,kBAAkB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}